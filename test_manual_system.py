"""
测试手动控制系统的基本功能
"""
import numpy as np
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """测试配置文件"""
    print("Testing config...")
    try:
        from config import (
            MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
            MANUAL_CONTROL_FREQUENCY, MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE,
            FORWARD_SPEED, DATA_DIR, TAKEOFF_HEIGHT, ADDITIONAL_INPUT_SIZE, OUTPUT_SIZE
        )
        print("✓ Config imported successfully")
        print(f"  Turn step: {MANUAL_CONTROL_TURN_STEP}°")
        print(f"  Pitch step: {MANUAL_CONTROL_PITCH_STEP}°")
        print(f"  Speed step: {MANUAL_CONTROL_SPEED_STEP} m/s")
        print(f"  Output size: {OUTPUT_SIZE}")
        print(f"  Additional input size: {ADDITIONAL_INPUT_SIZE}")
        return True
    except Exception as e:
        print(f"✗ Config import failed: {e}")
        return False

def test_student_model():
    """测试学生模型"""
    print("\nTesting student model...")
    try:
        from models.student_model import StudentModel, DepthDataset
        
        # 创建模型
        model = StudentModel()
        print("✓ Student model created successfully")
        
        # 测试预测
        dummy_depth = np.random.rand(72, 128).astype(np.float32)
        dummy_height = 1.5
        
        prediction = model.predict(dummy_depth, dummy_height)
        print(f"✓ Prediction successful: {prediction}")
        
        # 测试数据集
        dummy_obs = np.random.rand(10, 72, 128).astype(np.float32)
        dummy_heights = np.random.rand(10).astype(np.float32)
        dummy_actions = np.random.rand(10, 3).astype(np.float32)
        
        dataset = DepthDataset(dummy_obs, dummy_heights, dummy_actions)
        print(f"✓ Dataset created with {len(dataset)} samples")
        
        return True
    except Exception as e:
        print(f"✗ Student model test failed: {e}")
        return False

def test_manual_data_collector():
    """测试手动数据收集器"""
    print("\nTesting manual data collector...")
    try:
        from manual_control import ManualDataCollector
        
        # 创建临时目录
        test_dir = "test_data"
        os.makedirs(test_dir, exist_ok=True)
        
        collector = ManualDataCollector(test_dir)
        print("✓ Manual data collector created")
        
        # 添加一些测试数据
        for i in range(5):
            dummy_obs = np.random.rand(72, 128).astype(np.float32)
            dummy_height = 1.5 + i * 0.1
            dummy_action = (i * 10.0, i * 5.0, 1.0 + i * 0.2)
            collector.add_sample(dummy_obs, dummy_height, dummy_action)
        
        print(f"✓ Added {len(collector.observations)} samples")
        
        # 保存数据
        saved_path = collector.save_data("test")
        print(f"✓ Data saved to {saved_path}")
        
        # 清理
        if os.path.exists(saved_path):
            os.remove(saved_path)
        os.rmdir(test_dir)
        
        return True
    except Exception as e:
        print(f"✗ Manual data collector test failed: {e}")
        return False

def test_environment_methods():
    """测试环境的新方法（不连接AirSim）"""
    print("\nTesting environment methods...")
    try:
        # 只测试导入，不实际连接
        from environment.drone_env import DroneEnvironment
        print("✓ DroneEnvironment imported successfully")
        
        # 检查是否有新方法
        methods = dir(DroneEnvironment)
        if 'step_manual' in methods:
            print("✓ step_manual method found")
        else:
            print("✗ step_manual method not found")
            return False
            
        if 'get_observation_with_height' in methods:
            print("✓ get_observation_with_height method found")
        else:
            print("✗ get_observation_with_height method not found")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Environment test failed: {e}")
        return False

def test_data_loading():
    """测试数据加载功能"""
    print("\nTesting data loading...")
    try:
        # 创建测试数据
        test_dir = "test_data"
        os.makedirs(test_dir, exist_ok=True)
        
        test_obs = np.random.rand(20, 72, 128).astype(np.float32)
        test_heights = np.random.rand(20).astype(np.float32)
        test_actions = np.random.rand(20, 3).astype(np.float32)
        
        test_file = os.path.join(test_dir, "test_data.npz")
        np.savez_compressed(test_file, 
                          observations=test_obs,
                          heights=test_heights,
                          actions=test_actions)
        
        # 测试加载
        from train_model_manual import load_manual_data
        obs, heights, actions = load_manual_data(test_file)
        
        print(f"✓ Data loaded: obs={obs.shape}, heights={heights.shape}, actions={actions.shape}")
        
        # 清理
        os.remove(test_file)
        os.rmdir(test_dir)
        
        return True
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 50)
    print("Testing Manual Control System")
    print("=" * 50)
    
    tests = [
        test_config,
        test_student_model,
        test_manual_data_collector,
        test_environment_methods,
        test_data_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! System is ready to use.")
        print("\nNext steps:")
        print("1. Start AirSim")
        print("2. Run: python collect_data_manual.py --test_mode")
        print("3. Test manual control")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
