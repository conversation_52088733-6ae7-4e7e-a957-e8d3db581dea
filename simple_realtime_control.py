"""
简化的实时控制脚本 - 使用getch实现单字符输入
"""
import os
import time
import argparse
import threading
import numpy as np
import sys

from environment.drone_env import DroneEnvironment
from manual_control_api import ManualDataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE, FORWARD_SPEED, DATA_DIR
)

def getch():
    """获取单个字符输入（跨平台）"""
    try:
        import termios, tty
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)
        try:
            tty.setraw(sys.stdin.fileno())
            ch = sys.stdin.read(1)
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
        return ch
    except ImportError:
        # Windows fallback
        import msvcrt
        return msvcrt.getch().decode('utf-8')

class SimpleRealtimeController:
    """
    简化的实时控制器
    """
    def __init__(self, env: DroneEnvironment, data_collector=None, test_mode=False):
        self.env = env
        self.data_collector = data_collector
        self.test_mode = test_mode
        
        # 控制状态
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        self.collecting_data = False
        self.running = False
        
        # 统计
        self.sample_count = 0
        
        # 飞行维持线程
        self.flight_thread = None
        
    def start(self):
        """启动控制"""
        self.running = True
        
        # 启动飞行维持线程
        self.flight_thread = threading.Thread(target=self._maintain_flight_loop)
        self.flight_thread.daemon = True
        self.flight_thread.start()
        
        print("\n实时控制已启动!")
        print("=" * 50)
        print("控制说明:")
        print("  a/d: 左转/右转10度")
        print("  w/s: 向上俯仰/向下俯仰10度")
        print("  u/j: 加速/减速0.2m/s")
        if not self.test_mode:
            print("  空格: 切换数据收集")
        print("  r: 重置状态")
        print("  ?: 显示状态")
        print("  q: 退出")
        print("=" * 50)
        print("按键立即生效，无需回车确认")
        print("当前速度会持续保持，直到手动改变")
        print("=" * 50)
        
        # 主输入循环
        while self.running:
            try:
                print(f"\n[俯仰:{self.current_pitch:.1f}° 速度:{self.current_speed:.1f}m/s 收集:{'开' if self.collecting_data else '关'}] 请按键: ", end='', flush=True)
                key = getch().lower()
                
                if key == 'q':
                    print("\n正在退出...")
                    break
                elif key == 'a':
                    self._turn_left()
                elif key == 'd':
                    self._turn_right()
                elif key == 'w':
                    self._pitch_up()
                elif key == 's':
                    self._pitch_down()
                elif key == 'u':
                    self._speed_up()
                elif key == 'j':
                    self._speed_down()
                elif key == ' ':
                    self._toggle_data_collection()
                elif key == 'r':
                    self._reset_state()
                elif key == '?':
                    self._show_status()
                elif key == '\x03':  # Ctrl+C
                    break
                else:
                    print(f"未知按键: {repr(key)}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"输入处理错误: {e}")
                
        self.stop()
        
    def stop(self):
        """停止控制"""
        self.running = False
        if self.flight_thread:
            self.flight_thread.join(timeout=2)
            
    def _turn_left(self):
        """左转"""
        yaw_delta = -MANUAL_CONTROL_TURN_STEP
        print(f"左转{MANUAL_CONTROL_TURN_STEP}°")
        self._execute_action(yaw_delta)
        
    def _turn_right(self):
        """右转"""
        yaw_delta = MANUAL_CONTROL_TURN_STEP
        print(f"右转{MANUAL_CONTROL_TURN_STEP}°")
        self._execute_action(yaw_delta)
        
    def _pitch_up(self):
        """向上俯仰"""
        self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
        print(f"向上俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        self._execute_action(0.0)
        
    def _pitch_down(self):
        """向下俯仰"""
        self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
        print(f"向下俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        self._execute_action(0.0)
        
    def _speed_up(self):
        """加速"""
        self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
        print(f"加速，当前速度: {self.current_speed:.1f}m/s")
        self._execute_action(0.0)
        
    def _speed_down(self):
        """减速"""
        self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
        print(f"减速，当前速度: {self.current_speed:.1f}m/s")
        self._execute_action(0.0)
        
    def _execute_action(self, yaw_delta):
        """执行动作"""
        try:
            # 获取当前观测和高度
            observation = self.env.get_observation()
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val
            
            # 执行动作
            next_observation, reward, done, info = self.env.step_manual(yaw_delta, self.current_pitch, self.current_speed)
            
            # 如果正在收集数据
            if self.collecting_data and self.data_collector and not self.test_mode:
                self.data_collector.add_sample(observation, current_height, (yaw_delta, self.current_pitch, self.current_speed))
                self.sample_count += 1
                if self.sample_count % 20 == 0:
                    print(f"已收集 {self.sample_count} 个样本")
            
            # 检查碰撞
            if done:
                print("检测到碰撞！正在重置...")
                self.env.reset()
                self._reset_state()
                
        except Exception as e:
            print(f"执行动作时出错: {e}")
            
    def _maintain_flight_loop(self):
        """维持飞行循环"""
        while self.running:
            try:
                time.sleep(1.0)  # 每秒维持一次飞行
                if self.running:
                    # 维持当前速度和俯仰角
                    self.env.step_manual(0.0, self.current_pitch, self.current_speed)
            except Exception as e:
                print(f"维持飞行时出错: {e}")
                break
                
    def _reset_state(self):
        """重置控制状态"""
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")
        
    def _toggle_data_collection(self):
        """切换数据收集模式"""
        if self.test_mode:
            print("测试模式下无法收集数据")
            return
            
        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")
        
    def _show_status(self):
        """显示当前状态"""
        try:
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            position = drone_state.kinematics_estimated.position
            current_height = -position.z_val
            
            print(f"\n当前状态:")
            print(f"  俯仰角: {self.current_pitch:.1f}°")
            print(f"  前进速度: {self.current_speed:.1f}m/s")
            print(f"  数据收集: {'开启' if self.collecting_data else '关闭'}")
            print(f"  已收集样本: {self.sample_count}")
            print(f"  当前高度: {current_height:.2f}m")
            print(f"  当前位置: x={position.x_val:.2f}, y={position.y_val:.2f}")
            print(f"  当前偏航角: {self.env.current_yaw:.1f}°")
        except Exception as e:
            print(f"无法获取状态: {e}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Simple realtime manual control")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=5000, help="Maximum samples to collect")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()
    
    # 创建数据收集器
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir)
    
    # 创建控制器
    controller = SimpleRealtimeController(env, data_collector, args.test_mode)
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        env.reset()
        
        print(f"\n{'='*60}")
        print("简化实时手动控制")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*60}")
        
        # 启动控制
        controller.start()
        
    except KeyboardInterrupt:
        print("\n用户中断...")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        print("程序已退出")

if __name__ == "__main__":
    main()
