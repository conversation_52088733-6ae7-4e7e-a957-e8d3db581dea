"""
实时手动控制脚本 - 支持按键立即响应和持续飞行
"""
import os
import time
import argparse
import threading
import numpy as np
import sys
import termios
import tty
import select

from environment.drone_env import DroneEnvironment
from manual_control_api import ManualDataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE, FORWARD_SPEED, DATA_DIR
)

class RealtimeController:
    """
    实时控制器
    """
    def __init__(self, env: DroneEnvironment, data_collector=None):
        self.env = env
        self.data_collector = data_collector
        
        # 控制状态
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        self.collecting_data = False
        self.running = False
        
        # 按键状态
        self.key_states = {
            'a': False, 'd': False, 'w': False, 's': False,
            'u': False, 'j': False
        }
        
        # 控制线程
        self.control_thread = None
        self.input_thread = None
        
        # 终端设置
        self.old_settings = None
        
        # 统计
        self.sample_count = 0
        
    def setup_terminal(self):
        """设置终端为非阻塞模式"""
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
    def restore_terminal(self):
        """恢复终端设置"""
        if self.old_settings:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
            
    def start(self):
        """启动实时控制"""
        self.running = True
        self.setup_terminal()
        
        # 启动控制线程
        self.control_thread = threading.Thread(target=self._control_loop)
        self.control_thread.daemon = True
        self.control_thread.start()
        
        # 启动输入线程
        self.input_thread = threading.Thread(target=self._input_loop)
        self.input_thread.daemon = True
        self.input_thread.start()
        
        print("\n实时控制已启动!")
        print("控制说明:")
        print("  a/d: 左转/右转")
        print("  w/s: 向上俯仰/向下俯仰")
        print("  u/j: 加速/减速")
        print("  空格: 切换数据收集")
        print("  r: 重置状态")
        print("  q: 退出")
        print("  ?: 显示状态")
        print("\n按键立即生效，长按可连续变化")
        print("=" * 50)
        
        try:
            # 主循环
            while self.running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop()
            
    def stop(self):
        """停止控制"""
        self.running = False
        if self.control_thread:
            self.control_thread.join(timeout=1)
        if self.input_thread:
            self.input_thread.join(timeout=1)
        self.restore_terminal()
        
    def _input_loop(self):
        """输入处理循环"""
        while self.running:
            try:
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    key = sys.stdin.read(1).lower()
                    self._handle_key(key)
            except:
                break
                
    def _handle_key(self, key):
        """处理按键"""
        if key == 'q':
            print("\n正在退出...")
            self.running = False
        elif key == 'r':
            self._reset_state()
        elif key == ' ':
            self._toggle_data_collection()
        elif key == '?':
            self._show_status()
        elif key in ['a', 'd', 'w', 's', 'u', 'j']:
            self._process_control_key(key)
            
    def _process_control_key(self, key):
        """处理控制按键"""
        if key == 'a':  # 左转
            yaw_delta = -MANUAL_CONTROL_TURN_STEP
            print(f"左转 {MANUAL_CONTROL_TURN_STEP}°")
        elif key == 'd':  # 右转
            yaw_delta = MANUAL_CONTROL_TURN_STEP
            print(f"右转 {MANUAL_CONTROL_TURN_STEP}°")
        elif key == 'w':  # 向上俯仰
            self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
            yaw_delta = 0.0
            print(f"向上俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        elif key == 's':  # 向下俯仰
            self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
            yaw_delta = 0.0
            print(f"向下俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        elif key == 'u':  # 加速
            self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
            yaw_delta = 0.0
            print(f"加速，当前速度: {self.current_speed:.1f}m/s")
        elif key == 'j':  # 减速
            self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
            yaw_delta = 0.0
            print(f"减速，当前速度: {self.current_speed:.1f}m/s")
        else:
            return
            
        # 立即执行动作
        self._execute_action(yaw_delta)
        
    def _execute_action(self, yaw_delta):
        """执行动作"""
        try:
            # 获取当前观测和高度
            observation = self.env.get_observation()
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val
            
            # 执行动作
            next_observation, reward, done, info = self.env.step_manual(yaw_delta, self.current_pitch, self.current_speed)
            
            # 如果正在收集数据
            if self.collecting_data and self.data_collector:
                self.data_collector.add_sample(observation, current_height, (yaw_delta, self.current_pitch, self.current_speed))
                self.sample_count += 1
                if self.sample_count % 50 == 0:
                    print(f"已收集 {self.sample_count} 个样本")
            
            # 检查碰撞
            if done:
                print("检测到碰撞！正在重置...")
                self.env.reset()
                self._reset_state()
                
        except Exception as e:
            print(f"执行动作时出错: {e}")
            
    def _control_loop(self):
        """持续控制循环 - 保持无人机飞行"""
        last_time = time.time()
        
        while self.running:
            try:
                current_time = time.time()
                dt = current_time - last_time
                
                # 每0.5秒执行一次持续飞行命令
                if dt >= 0.5:
                    self._maintain_flight()
                    last_time = current_time
                    
                time.sleep(0.1)
            except Exception as e:
                print(f"控制循环出错: {e}")
                break
                
    def _maintain_flight(self):
        """维持飞行状态"""
        try:
            # 持续执行当前速度和俯仰角，转向角为0（保持当前方向）
            self.env.step_manual(0.0, self.current_pitch, self.current_speed)
        except Exception as e:
            print(f"维持飞行时出错: {e}")
            
    def _reset_state(self):
        """重置控制状态"""
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")
        
    def _toggle_data_collection(self):
        """切换数据收集模式"""
        if self.data_collector is None:
            print("测试模式下无法收集数据")
            return
            
        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")
        
    def _show_status(self):
        """显示当前状态"""
        try:
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            position = drone_state.kinematics_estimated.position
            current_height = -position.z_val
            
            print(f"\n当前状态:")
            print(f"  俯仰角: {self.current_pitch:.1f}°")
            print(f"  前进速度: {self.current_speed:.1f}m/s")
            print(f"  数据收集: {'开启' if self.collecting_data else '关闭'}")
            print(f"  已收集样本: {self.sample_count}")
            print(f"  当前高度: {current_height:.2f}m")
            print(f"  当前位置: x={position.x_val:.2f}, y={position.y_val:.2f}")
            print(f"  当前偏航角: {self.env.current_yaw:.1f}°")
        except Exception as e:
            print(f"无法获取状态: {e}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Realtime manual control for drone")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=5000, help="Maximum samples to collect")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()
    
    # 创建数据收集器
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir)
    
    # 创建控制器
    controller = RealtimeController(env, data_collector)
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        env.reset()
        
        print(f"\n{'='*60}")
        print("实时手动控制数据收集")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*60}")
        
        # 启动控制
        controller.start()
        
    except KeyboardInterrupt:
        print("\n用户中断...")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        print("程序已退出")

if __name__ == "__main__":
    main()
