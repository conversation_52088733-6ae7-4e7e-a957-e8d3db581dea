"""
手动控制无人机数据收集模块
"""
import os
import time
import threading
import argparse
import numpy as np
from typing import Dict, List, Tuple, Optional
from pynput import keyboard
import signal
import sys

from environment.drone_env import DroneEnvironment
from training.data_collector import DataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MANUAL_CONTROL_FREQUENCY, MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE,
    FORWARD_SPEED, DATA_DIR, TAKEOFF_HEIGHT
)

class ManualController:
    """
    手动控制器，支持键盘控制无人机
    """
    def __init__(self, env: DroneEnvironment, data_collector: Optional[DataCollector] = None):
        """
        初始化手动控制器

        Args:
            env: 无人机环境
            data_collector: 数据收集器（可选）
        """
        self.env = env
        self.data_collector = data_collector

        # 控制状态
        self.current_yaw_delta = 0.0  # 当前转向角增量
        self.current_pitch = 0.0  # 当前俯仰角
        self.current_speed = FORWARD_SPEED  # 当前前进速度

        # 按键状态
        self.key_states = {
            'a': False,  # 左转
            'd': False,  # 右转
            'w': False,  # 向上俯仰
            's': False,  # 向下俯仰
            'up': False,  # 加速
            'down': False,  # 减速
        }

        # 控制线程
        self.control_thread = None
        self.running = False

        # 数据收集模式
        self.collecting_data = False

        # 防抖动相关
        self.last_key_time = {}
        self.key_repeat_interval = 1.0 / MANUAL_CONTROL_FREQUENCY

        print("手动控制器初始化完成")
        print("控制说明:")
        print("  A/D: 左转/右转")
        print("  W/S: 向上俯仰/向下俯仰")
        print("  ↑/↓: 加速/减速")
        print("  Space: 切换数据收集模式")
        print("  ESC: 退出")

    def start_control(self):
        """
        开始手动控制
        """
        self.running = True

        # 注册按键事件
        self._register_key_events()

        # 启动控制线程
        self.control_thread = threading.Thread(target=self._control_loop)
        self.control_thread.daemon = True
        self.control_thread.start()

        print("手动控制已启动")

    def stop_control(self):
        """
        停止手动控制
        """
        self.running = False
        if self.control_thread:
            self.control_thread.join()

        # 停止键盘监听器
        if hasattr(self, 'listener'):
            self.listener.stop()

        print("手动控制已停止")

    def _register_key_events(self):
        """
        注册按键事件
        """
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char:
                    if key.char == 'a':
                        self._on_key_press('a')
                    elif key.char == 'd':
                        self._on_key_press('d')
                    elif key.char == 'w':
                        self._on_key_press('w')
                    elif key.char == 's':
                        self._on_key_press('s')
                    elif key.char == ' ':
                        self._toggle_data_collection()
                elif key == keyboard.Key.up:
                    self._on_key_press('up')
                elif key == keyboard.Key.down:
                    self._on_key_press('down')
                elif key == keyboard.Key.esc:
                    self._exit_program()
            except AttributeError:
                pass

        def on_release(key):
            try:
                if hasattr(key, 'char') and key.char:
                    if key.char == 'a':
                        self._on_key_release('a')
                    elif key.char == 'd':
                        self._on_key_release('d')
                    elif key.char == 'w':
                        self._on_key_release('w')
                    elif key.char == 's':
                        self._on_key_release('s')
                elif key == keyboard.Key.up:
                    self._on_key_release('up')
                elif key == keyboard.Key.down:
                    self._on_key_release('down')
            except AttributeError:
                pass

        # 启动键盘监听器
        self.listener = keyboard.Listener(on_press=on_press, on_release=on_release)
        self.listener.start()

    def _on_key_press(self, key: str):
        """
        按键按下事件
        """
        current_time = time.time()

        # 检查是否是新的按键按下（防抖动）
        if key not in self.last_key_time or current_time - self.last_key_time[key] > 0.1:
            self.key_states[key] = True
            self.last_key_time[key] = current_time
            self._process_single_key_press(key)

    def _on_key_release(self, key: str):
        """
        按键释放事件
        """
        self.key_states[key] = False

    def _process_single_key_press(self, key: str):
        """
        处理单次按键按下
        """
        if key == 'a':  # 左转
            self.current_yaw_delta -= MANUAL_CONTROL_TURN_STEP
        elif key == 'd':  # 右转
            self.current_yaw_delta += MANUAL_CONTROL_TURN_STEP
        elif key == 'w':  # 向上俯仰
            self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
        elif key == 's':  # 向下俯仰
            self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
        elif key == 'up':  # 加速
            self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
        elif key == 'down':  # 减速
            self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)

        print(f"控制更新: 转向={self.current_yaw_delta:.1f}°, 俯仰={self.current_pitch:.1f}°, 速度={self.current_speed:.1f}m/s")

    def _control_loop(self):
        """
        控制循环，处理长按按键
        """
        while self.running:
            current_time = time.time()

            # 检查长按按键
            for key, is_pressed in self.key_states.items():
                if is_pressed and key in self.last_key_time:
                    if current_time - self.last_key_time[key] > self.key_repeat_interval:
                        self._process_single_key_press(key)
                        self.last_key_time[key] = current_time

            time.sleep(0.01)  # 10ms循环间隔

    def _toggle_data_collection(self):
        """
        切换数据收集模式
        """
        if self.data_collector is None:
            print("警告: 未设置数据收集器，无法切换数据收集模式")
            return

        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")

    def _exit_program(self):
        """
        退出程序
        """
        print("正在退出程序...")
        self.running = False

    def get_current_action(self) -> Tuple[float, float, float]:
        """
        获取当前动作

        Returns:
            (转向角, 俯仰角, 前进速度)
        """
        # 重置转向角增量（每次获取后重置）
        yaw_delta = self.current_yaw_delta
        self.current_yaw_delta = 0.0

        return yaw_delta, self.current_pitch, self.current_speed

    def is_collecting_data(self) -> bool:
        """
        检查是否正在收集数据
        """
        return self.collecting_data

    def reset_after_collision(self):
        """
        碰撞后重置控制状态
        """
        self.current_yaw_delta = 0.0
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")


class ManualDataCollector:
    """
    手动数据收集器
    """
    def __init__(self, data_dir: str = DATA_DIR, resume_from: Optional[str] = None):
        """
        初始化手动数据收集器

        Args:
            data_dir: 数据存储目录
            resume_from: 要继续的数据文件路径（可选）
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)

        # 数据存储
        self.observations = []  # 深度图像
        self.heights = []  # 高度值
        self.actions = []  # 动作 [转向角, 俯仰角, 前进速度]

        # 如果指定了要继续的文件，加载现有数据
        if resume_from and os.path.exists(resume_from):
            self._load_existing_data(resume_from)

    def _load_existing_data(self, filepath: str):
        """
        加载现有数据
        """
        try:
            data = np.load(filepath)
            self.observations = data["observations"].tolist()
            self.heights = data["heights"].tolist()
            self.actions = data["actions"].tolist()
            print(f"已加载现有数据: {len(self.observations)} 个样本")
        except Exception as e:
            print(f"加载现有数据失败: {e}")

    def add_sample(self, observation: np.ndarray, height: float, action: Tuple[float, float, float]):
        """
        添加样本
        """
        self.observations.append(observation)
        self.heights.append(height)
        self.actions.append(list(action))

    def save_data(self, suffix: str = "manual"):
        """
        保存数据
        """
        if len(self.observations) == 0:
            print("没有数据可保存")
            return

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"manual_data_{timestamp}_{suffix}.npz"
        filepath = os.path.join(self.data_dir, filename)

        data = {
            "observations": np.array(self.observations),
            "heights": np.array(self.heights),
            "actions": np.array(self.actions)
        }

        np.savez_compressed(filepath, **data)
        print(f"数据已保存到: {filepath}")
        print(f"样本数量: {len(self.observations)}")

        return filepath
