"""
简单的交互式手动控制脚本
"""
import os
import time
import argparse
import numpy as np

from environment.drone_env import DroneEnvironment
from manual_control_api import ManualDataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE, FORWARD_SPEED, DATA_DIR
)

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Simple manual control for drone")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=5000, help="Maximum samples to collect")
    
    return parser.parse_args()

def show_help():
    """显示帮助信息"""
    print("\n" + "="*50)
    print("控制命令:")
    print("  a       - 左转10度")
    print("  d       - 右转10度")
    print("  w       - 向上俯仰10度")
    print("  s       - 向下俯仰10度")
    print("  u       - 加速0.2m/s")
    print("  j       - 减速0.2m/s")
    print("  go      - 执行当前设置的动作")
    print("  collect - 切换数据收集模式")
    print("  reset   - 重置控制状态")
    print("  status  - 显示当前状态")
    print("  help    - 显示此帮助")
    print("  quit    - 退出程序")
    print("="*50 + "\n")

def show_status(yaw_delta, pitch, speed, collecting, env, sample_count):
    """显示当前状态"""
    print(f"\n当前控制状态:")
    print(f"  转向角增量: {yaw_delta:.1f}度")
    print(f"  俯仰角: {pitch:.1f}度")
    print(f"  前进速度: {speed:.1f}m/s")
    print(f"  数据收集: {'开启' if collecting else '关闭'}")
    print(f"  已收集样本: {sample_count}")
    
    try:
        drone_state = env.client.getMultirotorState(vehicle_name=env.drone_name)
        position = drone_state.kinematics_estimated.position
        current_height = -position.z_val
        print(f"  当前高度: {current_height:.2f}m")
        print(f"  当前位置: x={position.x_val:.2f}, y={position.y_val:.2f}")
        print(f"  当前偏航角: {env.current_yaw:.1f}度")
    except Exception as e:
        print(f"  无法获取无人机状态: {e}")
    print()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()
    
    # 创建数据收集器
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir)
    
    # 控制状态
    yaw_delta = 0.0
    pitch = 0.0
    speed = FORWARD_SPEED
    collecting_data = False
    sample_count = 0
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        env.reset()
        
        print(f"\n{'='*60}")
        print("简单手动控制已启动")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 可收集数据模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*60}")
        
        show_help()
        
        while True:
            try:
                # 获取用户输入
                command = input("请输入命令 > ").strip().lower()
                
                if not command:
                    continue
                
                # 处理命令
                if command == 'a':  # 左转
                    yaw_delta -= MANUAL_CONTROL_TURN_STEP
                    print(f"设置左转 {MANUAL_CONTROL_TURN_STEP}度，当前转向增量: {yaw_delta:.1f}度")
                    
                elif command == 'd':  # 右转
                    yaw_delta += MANUAL_CONTROL_TURN_STEP
                    print(f"设置右转 {MANUAL_CONTROL_TURN_STEP}度，当前转向增量: {yaw_delta:.1f}度")
                    
                elif command == 'w':  # 向上俯仰
                    pitch = min(pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
                    print(f"设置向上俯仰，当前俯仰角: {pitch:.1f}度")
                    
                elif command == 's':  # 向下俯仰
                    pitch = max(pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
                    print(f"设置向下俯仰，当前俯仰角: {pitch:.1f}度")
                    
                elif command == 'u':  # 加速
                    speed = min(speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
                    print(f"设置加速，当前速度: {speed:.1f}m/s")
                    
                elif command == 'j':  # 减速
                    speed = max(speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
                    print(f"设置减速，当前速度: {speed:.1f}m/s")
                    
                elif command == 'go':  # 执行动作
                    print(f"执行动作: 转向={yaw_delta:.1f}°, 俯仰={pitch:.1f}°, 速度={speed:.1f}m/s")
                    
                    # 获取当前观测和高度
                    observation = env.get_observation()
                    drone_state = env.client.getMultirotorState(vehicle_name=env.drone_name)
                    current_height = -drone_state.kinematics_estimated.position.z_val
                    
                    # 执行动作
                    next_observation, reward, done, info = env.step_manual(yaw_delta, pitch, speed)
                    
                    # 如果正在收集数据
                    if collecting_data and data_collector and not args.test_mode:
                        data_collector.add_sample(observation, current_height, (yaw_delta, pitch, speed))
                        sample_count += 1
                        print(f"已收集样本: {sample_count}")
                        
                        if sample_count >= args.max_samples:
                            print(f"已达到最大样本数 {args.max_samples}")
                            break
                    
                    # 重置转向角增量
                    yaw_delta = 0.0
                    
                    # 检查碰撞
                    if done:
                        print("检测到碰撞！正在重置...")
                        env.reset()
                        yaw_delta = 0.0
                        pitch = 0.0
                        speed = FORWARD_SPEED
                    
                    print(f"奖励: {reward:.2f}")
                    
                elif command == 'collect':  # 切换数据收集
                    if args.test_mode:
                        print("测试模式下无法收集数据")
                    else:
                        collecting_data = not collecting_data
                        mode_str = "开启" if collecting_data else "关闭"
                        print(f"数据收集模式已{mode_str}")
                        
                elif command == 'reset':  # 重置控制状态
                    yaw_delta = 0.0
                    pitch = 0.0
                    speed = FORWARD_SPEED
                    print("控制状态已重置")
                    
                elif command == 'status':  # 显示状态
                    show_status(yaw_delta, pitch, speed, collecting_data, env, sample_count)
                    
                elif command == 'help':  # 显示帮助
                    show_help()
                    
                elif command == 'quit' or command == 'exit':  # 退出
                    break
                    
                else:
                    print(f"未知命令: {command}，输入help查看帮助")
                    
            except KeyboardInterrupt:
                print("\n用户中断...")
                break
            except EOFError:
                print("\n输入结束...")
                break
            except Exception as e:
                print(f"处理命令时出错: {e}")
                
    finally:
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        print("程序已退出")

if __name__ == "__main__":
    main()
