"""
运行学生模型（支持新的输入输出格式）
"""
import os
import time
import argparse
import numpy as np
from typing import List, Tuple

from environment.drone_env import DroneEnvironment
from models.student_model import StudentModel
from utils.visualization import visualize_drone_state, plot_trajectory

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Run student model with manual control format")
    parser.add_argument("--model_path", type=str, required=True, help="Path to trained model")
    parser.add_argument("--max_steps", type=int, default=1000, help="Maximum steps to run")
    parser.add_argument("--visualize", action="store_true", help="Visualize drone state")
    parser.add_argument("--save_path", type=str, default="results/student_manual", help="Path to save results")

    return parser.parse_args()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()

    # 创建保存目录
    os.makedirs(args.save_path, exist_ok=True)

    # 创建环境和模型
    env = DroneEnvironment()
    student = StudentModel()

    # 加载模型
    print(f"Loading model from {args.model_path}...")
    student.load(args.model_path)

    # 重置环境
    observation = env.reset()

    # 记录轨迹和动作
    positions = []
    actions_history = []

    try:
        # 运行模型
        for step in range(args.max_steps):
            # 获取当前位置和高度
            drone_state = env.client.getMultirotorState(vehicle_name=env.drone_name)
            position = drone_state.kinematics_estimated.position
            current_height = -position.z_val
            positions.append((position.x_val, position.y_val))

            # 使用学生模型预测动作
            yaw_delta, pitch, speed = student.predict(observation, current_height)
            actions_history.append((yaw_delta, pitch, speed))

            # 可视化
            if args.visualize and step % 10 == 0:
                # 创建步骤目录
                step_dir = os.path.join(args.save_path, f"step_{step:04d}")
                os.makedirs(step_dir, exist_ok=True)

                # 可视化无人机状态
                visualize_path = os.path.join(step_dir, "drone_state.png")
                visualize_drone_state(
                    (position.x_val, position.y_val, position.z_val),
                    env.current_yaw,
                    observation,
                    save_path=visualize_path,
                    distance_array=env.last_distance_array
                )

                # 保存深度图像
                depth_img_path = os.path.join(step_dir, "depth_image.png")
                import matplotlib.pyplot as plt
                plt.figure(figsize=(8, 6))
                plt.imshow(observation, cmap='viridis')
                plt.colorbar(label='Depth (m)')
                plt.title(f'Depth Image - Step {step}')
                plt.savefig(depth_img_path)
                plt.close()

            # 执行动作
            next_observation, reward, done, info = env.step_manual(yaw_delta, pitch, speed)

            # 打印信息
            print(f"Step {step + 1}/{args.max_steps}")
            print(f"  Height: {current_height:.2f}m")
            print(f"  Action: turn={yaw_delta:.2f}°, pitch={pitch:.2f}°, speed={speed:.2f}m/s")
            print(f"  Reward: {reward:.2f}")

            # 更新观测
            observation = next_observation

            # 如果回合结束，退出循环
            if done:
                print(f"Episode ended after {step + 1} steps due to collision")
                break

            # 等待一小段时间，以便观察
            time.sleep(0.05)

    except KeyboardInterrupt:
        print("Interrupted by user")

    finally:
        # 绘制轨迹
        trajectory_path = os.path.join(args.save_path, "trajectory.png")
        plot_trajectory(positions, save_path=trajectory_path)

        # 保存动作历史
        if actions_history:
            actions_array = np.array(actions_history)
            actions_path = os.path.join(args.save_path, "actions_history.npz")
            np.savez(actions_path, 
                    turn_angles=actions_array[:, 0],
                    pitch_angles=actions_array[:, 1], 
                    speeds=actions_array[:, 2])
            
            # 绘制动作历史
            import matplotlib.pyplot as plt
            fig, axes = plt.subplots(3, 1, figsize=(12, 10))
            
            steps = range(len(actions_array))
            
            axes[0].plot(steps, actions_array[:, 0])
            axes[0].set_title('Turn Angle Over Time')
            axes[0].set_ylabel('Turn Angle (degrees)')
            axes[0].grid(True)
            
            axes[1].plot(steps, actions_array[:, 1])
            axes[1].set_title('Pitch Angle Over Time')
            axes[1].set_ylabel('Pitch Angle (degrees)')
            axes[1].grid(True)
            
            axes[2].plot(steps, actions_array[:, 2])
            axes[2].set_title('Speed Over Time')
            axes[2].set_ylabel('Speed (m/s)')
            axes[2].set_xlabel('Step')
            axes[2].grid(True)
            
            plt.tight_layout()
            actions_plot_path = os.path.join(args.save_path, "actions_plot.png")
            plt.savefig(actions_plot_path)
            plt.close()
            
            print(f"Actions history saved to {actions_path}")
            print(f"Actions plot saved to {actions_plot_path}")

        # 关闭环境
        env.close()

        print("Run completed")

if __name__ == "__main__":
    main()
