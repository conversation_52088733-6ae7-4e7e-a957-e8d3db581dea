# 深度相机无人机手动控制数据收集项目

本项目实现了基于深度相机的无人机手动控制数据收集系统，支持SSH环境下的实时键盘控制。

## 项目概述

- 无人机起飞到1.5m高度，支持手动控制飞行
- 使用深度相机获取前方环境的深度图像
- 支持实时键盘控制：转向、俯仰、速度调节
- 收集深度图像、高度值和对应的控制动作作为训练数据
- 支持断点续存，可从中断处继续收集数据
- 适用于SSH远程连接环境，无需GUI界面

## 项目结构

```
deep_il_1/
├── environment/           # 无人机环境封装
│   ├── __init__.py
│   ├── drone_env.py       # 无人机环境封装
│   └── depth_utils.py     # 深度图像处理工具
├── models/                # 模型定义
│   ├── __init__.py
│   ├── teacher_model.py   # 规则算法作为教师模型
│   └── student_model.py   # 学生模型（CNN+FC神经网络）
├── training/              # 训练相关
│   ├── __init__.py
│   ├── data_collector.py  # 数据收集
│   └── trainer.py         # 模型训练
├── utils/                 # 工具函数
│   ├── __init__.py
│   └── visualization.py   # 可视化工具
├── data/                  # 数据存储目录
├── saved_models/          # 保存的模型
├── config.py              # 配置文件
├── ssh_realtime_control.py # SSH实时手动控制脚本（主要使用）
├── collect_data.py        # 自动收集训练数据（教师模型）
├── train_model.py         # 训练学生模型
├── run_student.py         # 运行学生模型
├── run_teacher.py         # 运行教师模型
└── settings.json          # AirSim配置文件
```

## 安装依赖

```bash
pip install airsim numpy torch matplotlib
```

## 手动控制使用方法

### 1. SSH实时手动控制（推荐）

#### 测试模式（不收集数据）
```bash
python ssh_realtime_control.py --test_mode
```

#### 数据收集模式
```bash
python ssh_realtime_control.py --max_samples 5000
```

#### 从现有数据继续收集
```bash
python ssh_realtime_control.py --resume_from data/manual_data_20231201_120000_final.npz --max_samples 5000
```

### 2. 控制说明

- **A/D**: 左转/右转（步长：10度）
- **W/S**: 向上俯仰/向下俯仰（步长：10度）
- **U/J**: 加速/减速（步长：0.2m/s）
- **空格**: 切换数据收集模式
- **ESC或Q**: 退出程序

### 3. 控制参数

- 转向角范围：无限制（累加到当前偏航角）
- 俯仰角范围：±30度
- 速度范围：0.2-3.0 m/s
- 初始速度：1.0 m/s
- 控制频率：4Hz（长按时的响应频率）

## 自动收集使用方法（教师模型）

### 1. 收集训练数据

```bash
python collect_data.py --episodes 100 --steps 50
```

### 2. 训练学生模型

```bash
python train_model.py --data_path data/depth_data_YYYYMMDD_HHMMSS_final.npz --epochs 100
```

### 3. 运行学生模型

```bash
python run_student.py --model_path saved_models/student_model_latest.pth --max_steps 200 --visualize
```

### 4. 运行教师模型

```bash
python run_teacher.py --max_steps 50 --visualize
```

## 数据格式

### 手动收集的数据
- **observations**: 深度图像数组 [N, H, W]
- **heights**: 高度值数组 [N]
- **actions**: 动作数组 [N, 3] (转向角, 俯仰角, 前进速度)

### 模型输入输出
- **输入**:
  - 深度图像：[1, 72, 128]
  - 高度值：[1]
- **输出**: [转向角, 俯仰角, 前进速度]

## 配置参数

可以在`config.py`文件中修改以下参数：

### 手动控制配置
```python
MANUAL_CONTROL_TURN_STEP = 10.0      # 转向步长（度）
MANUAL_CONTROL_PITCH_STEP = 10.0     # 俯仰步长（度）
MANUAL_CONTROL_SPEED_STEP = 0.2      # 速度步长（米/秒）
MANUAL_CONTROL_FREQUENCY = 4.0       # 长按频率（Hz）
MIN_FORWARD_SPEED = 0.2              # 最小前进速度
MAX_FORWARD_SPEED = 3.0              # 最大前进速度
MAX_PITCH_ANGLE = 30.0               # 最大俯仰角
```

### 其他配置
- 无人机配置：起飞高度、前进速度、无人机半径
- 相机配置：相机名称、视场角、深度图像分辨率
- 学生模型配置：CNN+FC架构参数、学习率、批次大小等
- 训练配置：数据收集回合数、训练轮数等

## 实现细节

### SSH实时控制

1. **终端设置**: 使用termios和tty将终端设置为原始模式，实现无需按Enter的实时按键检测
2. **多线程架构**:
   - 按键监听线程：实时检测按键输入
   - 控制循环线程：处理控制逻辑和无人机动作
   - 主线程：管理程序生命周期
3. **按键处理**: 使用select实现非阻塞按键检测，支持方向键和字母键
4. **控制频率**: 4Hz的控制频率，确保平滑的飞行控制

### 手动控制数据收集

1. **数据格式**: 收集深度图像、高度值和3维动作（转向角、俯仰角、前进速度）
2. **断点续存**: 支持从现有数据文件继续收集，避免数据丢失
3. **实时反馈**: 显示当前控制状态和已收集样本数量
4. **碰撞处理**: 检测到碰撞后自动重置环境和控制状态

### 深度图像处理

1. 获取深度图像：使用AirSim的API获取深度图像
2. 处理深度图像：将超过100米的深度值（天空）设为100米
3. 数据存储：以numpy数组格式存储，便于后续训练使用

### 学生模型

学生模型使用CNN+FC神经网络架构：
1. 输入为深度图像（单通道）+ 高度值
2. 使用卷积层提取特征
3. 使用全连接层输出3维动作

## 特性说明

### SSH兼容性
- 无需GUI界面，完全基于终端操作
- 支持远程SSH连接使用
- 实时按键响应，无需按Enter确认

### 控制精度
- 转向角步长：10度
- 俯仰角步长：10度
- 速度步长：0.2m/s
- 控制频率：4Hz

### 数据质量
- 实时收集人工控制数据
- 支持长时间连续收集
- 自动保存和备份机制

## 其他说明

- `test_depth_image.py`：测试深度图像的获取和处理
- `simple_check.py`：检查收集的数据文件完整性
- `test_drone_600m.py`：高空俯瞰测试脚本