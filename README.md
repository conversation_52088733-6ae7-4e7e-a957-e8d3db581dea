# 深度相机无人机避障项目

本项目实现了基于深度相机的无人机避障系统，使用模仿学习方法训练避障模型。

## 项目概述

- 无人机起飞到5m高度，以2m/s的速度前进
- 使用深度相机获取前方环境的深度图像
- 将深度图像转换为3D点云，在3D空间中检测潜在碰撞区域
- 基于点云数据进行避障决策
- 采用模仿学习方法，先设计规则算法作为教师模型，然后训练学生模型

## 项目结构

```
deep_il/
├── environment/           # 无人机环境封装
│   ├── __init__.py
│   ├── drone_env.py       # 无人机环境封装
│   └── depth_utils.py     # 深度图像处理工具
├── models/                # 模型定义
│   ├── __init__.py
│   ├── teacher_model.py   # 规则算法作为教师模型
│   └── student_model.py   # 学生模型（CNN+FC神经网络）
├── training/              # 训练相关
│   ├── __init__.py
│   ├── data_collector.py  # 数据收集
│   └── trainer.py         # 模型训练
├── utils/                 # 工具函数
│   ├── __init__.py
│   └── visualization.py   # 可视化工具
├── config.py              # 配置文件
├── run_teacher.py         # 运行教师模型
├── collect_data.py        # 收集训练数据
├── train_model.py         # 训练学生模型
├── run_student.py         # 运行学生模型
├── test_depth_image.py    # 深度图像测试脚本
└── settings_rgb_deep.json # AirSim配置文件
```

## 安装依赖

```bash
pip install airsim numpy torch matplotlib
```

## 使用方法

### 1. 运行教师模型

```bash
python run_teacher.py --max_steps 50 --visualize
```

### 2. 收集训练数据

```bash
python collect_data.py --episodes 100 --steps 50
```

### 3. 训练学生模型

```bash
python train_model.py --data_path data/depth_data_YYYYMMDD_HHMMSS_final.npz --epochs 100
```

### 4. 运行学生模型

```bash
python run_student.py --model_path saved_models/student_model_latest.pth --max_steps 200 --visualize
```

## 配置参数

可以在`config.py`文件中修改以下参数：

- 无人机配置：起飞高度、前进速度、无人机半径
- 相机配置：相机名称、视场角、深度图像分辨率、深度缩放因子
- 点云配置：角度分区数量、角度范围、碰撞检测方向数量
- 避障配置：最小障碍物距离、最大转向角
- 学生模型配置：CNN+FC架构参数、学习率、批次大小等
- 训练配置：数据收集回合数、训练轮数等

## 实现细节

### 深度图像处理

1. 获取深度图像：使用AirSim的API获取深度图像
2. 处理深度图像：将超过100米的深度值（天空）设为1米
3. 转换为点云：将深度图像转换为3D点云
4. 碰撞检测：在3D空间中检测潜在碰撞区域

### 教师模型

教师模型基于规则算法，根据点云数据计算安全飞行方向：
1. 对每个可能的飞行方向，计算无人机体积在该方向上的投影
2. 检查点云中是否有点落在这个投影区域内
3. 如果有，表示该方向可能发生碰撞
4. 对所有可能的方向进行评分，选择得分最高的方向作为避障指令

### 学生模型

学生模型使用CNN+FC神经网络架构：
1. 输入为深度图像（单通道）
2. 使用卷积层提取特征
3. 使用全连接层输出转向角

### 数据收集

使用教师模型控制无人机飞行，收集深度图像和对应的转向角作为训练数据。

### 模型训练

使用收集的数据训练学生模型，使其模仿教师模型的行为。

## 其他说明

- `test_depth_image.py`：测试深度图像的获取和处理，验证深度图像的缩放因子和有效范围
- `simple_check.py`：检查data下收集的数据最后收集成功的文件内容是不是前面收集的所有的总和
- `test_drone_600m.py`：起飞600米高度来俯瞰下方环境布局（需配合settings_downrgb.json的下视相机使用）

## 深度图像说明

- 深度图像中深度值超过100米的被视作天空，将被强制设为100米的深度值
- 经测试，深度图像不需要缩放，测量出来的深度值即为实际值
- 无人机的速度始终是2m/s，不随与障碍物的距离而改变
- 速度方向始终是无人机机体的正前方，只需要根据检测得到的安全飞行方向改变无人机朝向即可


## 个人对项目设计思路的总结：
environment/depth_utils.py：首先将深度图像转为3d点云，然后划分为180度，实际上视场角只是90度，有90度的信息是空的，然后180度水平分为72个区间每个区间2.5度，然后每个区间取该区间内水平距离最小的点的水平距离作为这个区间与无人机的距离。（水平距离是忽略z轴，只算xy轴的欧氏距离）设垂直视场角为45度，即上下各22.5度。但实际配置的视场角没确定。取每个方向与点的方向的角度差在90度内的点，（当然我的视场角在90度，这90度内的方向与所有点的方向的角度差都在90度内）然后再取垂直视角内的点，之后就根据与该方向的角度差和水平距离计算出这个方向每个点的风险度，然后取最大的风险度作为这个方向的风险度，然后相邻三个方向的风险滑动平均。（根据这个逻辑，我觉得方向取360度不好，比如在距离视场角最边缘的点的的角度差为90度的不在视场角内的方向，它的风险就会很小，因为角度差大）不过后面会计算每个方向的安全度，把不在转向角范围内的方向的安全度置零了，并且会根据转向角大小对每个方向的安全度再进行衰减，转向角越大，安全度的衰减越大。所以取360度好像也没影响。不过这36个方向的角度好像是绝对角度，不随无人机的转动而改变，好像也不能减少。