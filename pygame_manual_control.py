"""
基于pygame的实时手动控制脚本
"""
import os
import time
import argparse
import numpy as np
import pygame
import sys

from environment.drone_env import DroneEnvironment
from manual_control_api import ManualDataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MANUAL_CONTROL_FREQUENCY, MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE,
    FORWARD_SPEED, DATA_DIR
)

class PygameController:
    """
    基于pygame的实时控制器
    """
    def __init__(self, env: DroneEnvironment, data_collector=None, test_mode=False):
        self.env = env
        self.data_collector = data_collector
        self.test_mode = test_mode

        # 控制状态
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        self.collecting_data = False
        self.running = False

        # 按键状态
        self.key_states = {}

        # 统计
        self.sample_count = 0

        # 速度比例和按键重复间隔
        self.speedup_ratio = 2.0  # 加速比例
        self.vehicle_yaw_rate = 30.0  # 偏航角速度
        self.scale_ratio = 1.0  # 缩放比例

        # 按键重复控制
        self.last_key_time = {}
        self.key_repeat_interval = 1.0 / MANUAL_CONTROL_FREQUENCY  # 4Hz

        # 初始化pygame
        os.environ['SDL_VIDEODRIVER'] = 'dummy'  # 使用虚拟显示驱动
        pygame.init()
        self.screen = pygame.display.set_mode((800, 600))
        pygame.display.set_caption("AirSim Manual Control")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)

    def start(self):
        """启动控制"""
        self.running = True

        print("\nPygame实时控制已启动!")
        print("=" * 50)
        print("控制说明:")
        print("  A/D: 左转/右转")
        print("  W/S: 向上俯仰/向下俯仰")
        print("  UP/DOWN: 加速/减速")
        print("  LEFT/RIGHT: 左右移动")
        print("  SPACE: 切换数据收集模式")
        print("  ESC: 退出")
        print("=" * 50)
        print("按键立即生效，长按可连续变化")
        print("关闭pygame窗口或按ESC退出")
        print("=" * 50)

        # 主循环
        while self.running:
            current_time = time.time()

            # 处理pygame事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    break

            # 获取当前按键状态
            keys = pygame.key.get_pressed()

            # 处理按键
            self._process_keys(keys, current_time)

            # 更新显示
            self._update_display()

            # 控制帧率
            self.clock.tick(60)  # 60 FPS

        pygame.quit()

    def _process_keys(self, keys, current_time):
        """处理按键输入"""
        # 检查空格键切换数据收集
        if keys[pygame.K_SPACE]:
            if 'space' not in self.last_key_time or current_time - self.last_key_time['space'] > 0.5:
                self._toggle_data_collection()
                self.last_key_time['space'] = current_time

        # 检查ESC退出
        if keys[pygame.K_ESCAPE]:
            self.running = False
            return

        # 计算速度缩放
        if keys[pygame.K_LSHIFT] or keys[pygame.K_RSHIFT]:
            scale_ratio = self.speedup_ratio
        else:
            scale_ratio = self.speedup_ratio / self.speedup_ratio

        # 处理转向 (A/D)
        yaw_rate = 0
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            if self._should_process_key('turn_left', current_time):
                yaw_rate = -(keys[pygame.K_a] or keys[pygame.K_LEFT]) * scale_ratio * self.vehicle_yaw_rate
                print("左转")

        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            if self._should_process_key('turn_right', current_time):
                yaw_rate = (keys[pygame.K_d] or keys[pygame.K_RIGHT]) * scale_ratio * self.vehicle_yaw_rate
                print("右转")

        # 处理俯仰 (W/S)
        if keys[pygame.K_w]:
            if self._should_process_key('pitch_up', current_time):
                self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
                print(f"向上俯仰，当前俯仰角: {self.current_pitch:.1f}°")

        if keys[pygame.K_s]:
            if self._should_process_key('pitch_down', current_time):
                self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
                print(f"向下俯仰，当前俯仰角: {self.current_pitch:.1f}°")

        # 处理速度 (UP/DOWN)
        if keys[pygame.K_UP]:
            if self._should_process_key('speed_up', current_time):
                self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
                print(f"加速，当前速度: {self.current_speed:.1f}m/s")

        if keys[pygame.K_DOWN]:
            if self._should_process_key('speed_down', current_time):
                self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
                print(f"减速，当前速度: {self.current_speed:.1f}m/s")

        # 如果有任何控制输入，执行动作
        if (keys[pygame.K_a] or keys[pygame.K_d] or keys[pygame.K_LEFT] or keys[pygame.K_RIGHT] or
            keys[pygame.K_w] or keys[pygame.K_s] or keys[pygame.K_UP] or keys[pygame.K_DOWN]):

            # 计算转向角增量
            yaw_delta = 0
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                yaw_delta = -MANUAL_CONTROL_TURN_STEP
            elif keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                yaw_delta = MANUAL_CONTROL_TURN_STEP

            # 执行动作
            self._execute_action(yaw_delta)

    def _should_process_key(self, key_name, current_time):
        """检查是否应该处理按键（防抖动）"""
        if key_name not in self.last_key_time:
            self.last_key_time[key_name] = current_time
            return True
        elif current_time - self.last_key_time[key_name] >= self.key_repeat_interval:
            self.last_key_time[key_name] = current_time
            return True
        return False

    def _execute_action(self, yaw_delta):
        """执行动作"""
        try:
            # 获取当前观测和高度
            observation = self.env.get_observation()
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val

            # 执行动作
            next_observation, reward, done, info = self.env.step_manual(yaw_delta, self.current_pitch, self.current_speed)

            # 如果正在收集数据
            if self.collecting_data and self.data_collector and not self.test_mode:
                self.data_collector.add_sample(observation, current_height, (yaw_delta, self.current_pitch, self.current_speed))
                self.sample_count += 1
                if self.sample_count % 50 == 0:
                    print(f"已收集 {self.sample_count} 个样本")

            # 检查碰撞
            if done:
                print("检测到碰撞！正在重置...")
                self.env.reset()
                self._reset_state()

        except Exception as e:
            print(f"执行动作时出错: {e}")

    def _reset_state(self):
        """重置控制状态"""
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")

    def _toggle_data_collection(self):
        """切换数据收集模式"""
        if self.test_mode:
            print("测试模式下无法收集数据")
            return

        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")

    def _update_display(self):
        """更新pygame显示"""
        # 清屏
        self.screen.fill((0, 0, 0))

        # 显示控制信息
        y_offset = 50
        texts = [
            "AirSim Manual Control",
            "",
            f"俯仰角: {self.current_pitch:.1f}°",
            f"前进速度: {self.current_speed:.1f}m/s",
            f"数据收集: {'开启' if self.collecting_data else '关闭'}",
            f"已收集样本: {self.sample_count}",
            "",
            "控制说明:",
            "A/D - 左转/右转",
            "W/S - 向上俯仰/向下俯仰",
            "↑/↓ - 加速/减速",
            "空格 - 切换数据收集",
            "ESC - 退出"
        ]

        for text in texts:
            if text:
                color = (255, 255, 255) if not text.startswith("数据收集") else (0, 255, 0) if self.collecting_data else (255, 0, 0)
                surface = self.font.render(text, True, color)
                self.screen.blit(surface, (50, y_offset))
            y_offset += 40

        pygame.display.flip()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Pygame-based manual control")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=5000, help="Maximum samples to collect")

    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()

    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()

    # 创建数据收集器
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir)

    # 创建控制器
    controller = PygameController(env, data_collector, args.test_mode)

    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        env.reset()

        print(f"\n{'='*60}")
        print("Pygame实时手动控制")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*60}")

        # 启动控制
        controller.start()

    except KeyboardInterrupt:
        print("\n用户中断...")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")

        # 关闭环境
        env.close()
        print("程序已退出")

if __name__ == "__main__":
    main()
