"""
直接手动控制脚本 - 简单直接的命令行控制
"""
import os
import time
import argparse
import numpy as np

from environment.drone_env import DroneEnvironment
from manual_control_api import ManualDataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE, FORWARD_SPEED, DATA_DIR
)

class DirectController:
    """
    直接控制器 - 每次输入立即执行
    """
    def __init__(self, env: DroneEnvironment, data_collector=None, test_mode=False):
        self.env = env
        self.data_collector = data_collector
        self.test_mode = test_mode
        
        # 控制状态
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        self.collecting_data = False
        
        # 统计
        self.sample_count = 0
        
    def start(self):
        """启动控制"""
        print("\n直接手动控制已启动!")
        print("=" * 50)
        print("控制命令:")
        print("  a - 左转10度并立即执行")
        print("  d - 右转10度并立即执行")
        print("  w - 向上俯仰10度并立即执行")
        print("  s - 向下俯仰10度并立即执行")
        print("  u - 加速0.2m/s并立即执行")
        print("  j - 减速0.2m/s并立即执行")
        print("  f - 保持当前设置继续飞行")
        if not self.test_mode:
            print("  c - 切换数据收集")
        print("  r - 重置状态")
        print("  ? - 显示状态")
        print("  q - 退出")
        print("=" * 50)
        print("每个命令立即执行，无人机会保持当前速度飞行")
        print("=" * 50)
        
        # 主循环
        while True:
            try:
                # 显示当前状态
                status = f"[俯仰:{self.current_pitch:.1f}° 速度:{self.current_speed:.1f}m/s"
                if not self.test_mode:
                    status += f" 收集:{'开' if self.collecting_data else '关'}"
                status += f" 样本:{self.sample_count}]"
                
                command = input(f"\n{status}\n请输入命令: ").strip().lower()
                
                if not command:
                    continue
                    
                if command == 'q':
                    print("正在退出...")
                    break
                elif command == 'a':
                    self._turn_left()
                elif command == 'd':
                    self._turn_right()
                elif command == 'w':
                    self._pitch_up()
                elif command == 's':
                    self._pitch_down()
                elif command == 'u':
                    self._speed_up()
                elif command == 'j':
                    self._speed_down()
                elif command == 'f':
                    self._fly_forward()
                elif command == 'c' and not self.test_mode:
                    self._toggle_data_collection()
                elif command == 'r':
                    self._reset_state()
                elif command == '?':
                    self._show_status()
                else:
                    print(f"未知命令: {command}")
                    
            except KeyboardInterrupt:
                print("\n用户中断...")
                break
            except EOFError:
                print("\n输入结束...")
                break
            except Exception as e:
                print(f"处理命令时出错: {e}")
                
    def _turn_left(self):
        """左转并立即执行"""
        yaw_delta = -MANUAL_CONTROL_TURN_STEP
        print(f"执行: 左转{MANUAL_CONTROL_TURN_STEP}°")
        self._execute_action(yaw_delta)
        
    def _turn_right(self):
        """右转并立即执行"""
        yaw_delta = MANUAL_CONTROL_TURN_STEP
        print(f"执行: 右转{MANUAL_CONTROL_TURN_STEP}°")
        self._execute_action(yaw_delta)
        
    def _pitch_up(self):
        """向上俯仰并立即执行"""
        self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
        print(f"执行: 向上俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        self._execute_action(0.0)
        
    def _pitch_down(self):
        """向下俯仰并立即执行"""
        self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
        print(f"执行: 向下俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        self._execute_action(0.0)
        
    def _speed_up(self):
        """加速并立即执行"""
        self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
        print(f"执行: 加速，当前速度: {self.current_speed:.1f}m/s")
        self._execute_action(0.0)
        
    def _speed_down(self):
        """减速并立即执行"""
        self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
        print(f"执行: 减速，当前速度: {self.current_speed:.1f}m/s")
        self._execute_action(0.0)
        
    def _fly_forward(self):
        """保持当前设置继续飞行"""
        print(f"执行: 保持当前设置飞行 (俯仰:{self.current_pitch:.1f}° 速度:{self.current_speed:.1f}m/s)")
        self._execute_action(0.0)
        
    def _execute_action(self, yaw_delta):
        """执行动作"""
        try:
            print(f"发送命令到AirSim: 转向={yaw_delta:.1f}°, 俯仰={self.current_pitch:.1f}°, 速度={self.current_speed:.1f}m/s")
            
            # 获取当前观测和高度
            observation = self.env.get_observation()
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val
            
            # 执行动作
            next_observation, reward, done, info = self.env.step_manual(yaw_delta, self.current_pitch, self.current_speed)
            
            print(f"动作执行完成，奖励: {reward:.2f}")
            
            # 如果正在收集数据
            if self.collecting_data and self.data_collector and not self.test_mode:
                self.data_collector.add_sample(observation, current_height, (yaw_delta, self.current_pitch, self.current_speed))
                self.sample_count += 1
                print(f"数据已收集，当前样本数: {self.sample_count}")
            
            # 检查碰撞
            if done:
                print("检测到碰撞！正在重置...")
                self.env.reset()
                self._reset_state()
                print("重置完成，可以继续控制")
                
        except Exception as e:
            print(f"执行动作时出错: {e}")
            import traceback
            traceback.print_exc()
            
    def _reset_state(self):
        """重置控制状态"""
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")
        
    def _toggle_data_collection(self):
        """切换数据收集模式"""
        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")
        
    def _show_status(self):
        """显示当前状态"""
        try:
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            position = drone_state.kinematics_estimated.position
            current_height = -position.z_val
            
            print(f"\n=== 当前状态 ===")
            print(f"俯仰角: {self.current_pitch:.1f}°")
            print(f"前进速度: {self.current_speed:.1f}m/s")
            print(f"数据收集: {'开启' if self.collecting_data else '关闭'}")
            print(f"已收集样本: {self.sample_count}")
            print(f"当前高度: {current_height:.2f}m")
            print(f"当前位置: x={position.x_val:.2f}, y={position.y_val:.2f}")
            print(f"当前偏航角: {self.env.current_yaw:.1f}°")
            print("================")
        except Exception as e:
            print(f"无法获取状态: {e}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Direct manual control")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=5000, help="Maximum samples to collect")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()
    
    # 创建数据收集器
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir)
    
    # 创建控制器
    controller = DirectController(env, data_collector, args.test_mode)
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        env.reset()
        
        print(f"\n{'='*60}")
        print("直接手动控制")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*60}")
        
        # 启动控制
        controller.start()
        
    except KeyboardInterrupt:
        print("\n用户中断...")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        print("程序已退出")

if __name__ == "__main__":
    main()
