# 手动控制数据收集系统

本系统支持通过键盘手动控制无人机进行数据收集，并训练支持新动作空间的学生模型。

## 新功能特性

### 1. 手动控制
- **键盘控制**：使用WASD和方向键控制无人机
- **防抖动**：单次点击变化一个步长，长按以4Hz频率连续变化
- **两种模式**：数据收集模式和测试模式
- **碰撞重置**：发生碰撞后自动回到初始位置重新起飞

### 2. 新的动作空间
- **输入**：深度图像 + 当前高度值
- **输出**：转向角 + 俯仰角 + 前进速度
- **控制方式**：机体坐标系下的3D运动控制

### 3. 断点续存
- 支持从现有数据文件继续收集数据
- 自动保存收集的数据，支持中断恢复

## 控制说明

### 键盘控制
- **A/D**：左转/右转（步长：10度）
- **W/S**：向上俯仰/向下俯仰（步长：10度）
- **↑/↓**：加速/减速（步长：0.2m/s）
- **空格键**：切换数据收集模式
- **ESC键**：退出程序

### 控制参数
- 转向角范围：无限制（累加到当前偏航角）
- 俯仰角范围：±30度
- 速度范围：0.2-3.0 m/s
- 初始速度：1.0 m/s（config中的FORWARD_SPEED）

## 使用方法

### 1. 安装依赖
```bash
pip install pynput matplotlib
```

### 2. 手动数据收集

#### 测试模式（不收集数据）
```bash
python collect_data_manual.py --test_mode
```

#### 数据收集模式
```bash
python collect_data_manual.py --max_samples 5000
```

#### 断点续存
```bash
python collect_data_manual.py --resume_from data/manual_data_20231201_120000_final.npz --max_samples 10000
```

### 3. 训练学生模型
```bash
python train_model_manual.py --data_path data/manual_data_20231201_120000_final.npz --epochs 100
```

### 4. 运行训练好的模型
```bash
python run_student_manual.py --model_path saved_models/student_model_manual_latest.pth --max_steps 500 --visualize
```

## 数据格式

### 收集的数据
- **observations**：深度图像数组 [N, H, W]
- **heights**：高度值数组 [N]
- **actions**：动作数组 [N, 3] (转向角, 俯仰角, 前进速度)

### 模型输入输出
- **输入**：
  - 深度图像：[1, 72, 128]
  - 高度值：[1]
- **输出**：[转向角, 俯仰角, 前进速度]

## 配置参数

在 `config.py` 中可以调整以下参数：

```python
# 手动控制配置
MANUAL_CONTROL_TURN_STEP = 10.0      # 转向步长（度）
MANUAL_CONTROL_PITCH_STEP = 10.0     # 俯仰步长（度）
MANUAL_CONTROL_SPEED_STEP = 0.2      # 速度步长（米/秒）
MANUAL_CONTROL_FREQUENCY = 4.0       # 长按频率（Hz）
MIN_FORWARD_SPEED = 0.2              # 最小前进速度
MAX_FORWARD_SPEED = 3.0              # 最大前进速度
MAX_PITCH_ANGLE = 30.0               # 最大俯仰角

# 学生模型配置
OUTPUT_SIZE = 3                      # 输出大小（3个动作）
ADDITIONAL_INPUT_SIZE = 1            # 额外输入大小（高度值）
```

## 文件说明

### 新增文件
- `manual_control.py`：手动控制模块
- `collect_data_manual.py`：手动数据收集脚本
- `train_model_manual.py`：训练脚本（支持新数据格式）
- `run_student_manual.py`：运行脚本（支持新输入输出）

### 修改文件
- `config.py`：添加手动控制和新模型配置
- `environment/drone_env.py`：添加`step_manual`方法
- `models/student_model.py`：支持深度图像+高度输入，3维动作输出

## 注意事项

1. **权限要求**：键盘监听可能需要管理员权限
2. **AirSim连接**：确保AirSim正在运行并可连接
3. **数据质量**：建议在不同环境和场景下收集数据
4. **碰撞处理**：系统会自动处理碰撞并重置，但频繁碰撞会影响数据质量
5. **存储空间**：深度图像数据较大，注意磁盘空间

## 故障排除

### 键盘控制无响应
- 确保程序有键盘输入权限
- 检查是否在正确的终端窗口中
- 尝试重启程序

### 无人机控制异常
- 检查AirSim连接状态
- 确认无人机已正确起飞
- 检查config中的参数设置

### 数据保存失败
- 检查磁盘空间
- 确认data目录存在且有写权限
- 检查文件路径是否正确
