"""
训练学生模型（支持手动收集的数据）
"""
import os
import time
import argparse
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple

from models.student_model import StudentModel
from config import MODEL_DIR, EPOCHS, BATCH_SIZE

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Train student model with manual data")
    parser.add_argument("--data_path", type=str, required=True, help="Path to training data (.npz file)")
    parser.add_argument("--epochs", type=int, default=EPOCHS, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=BATCH_SIZE, help="Batch size")
    parser.add_argument("--model_dir", type=str, default=MODEL_DIR, help="Directory to save model")
    parser.add_argument("--test_split", type=float, default=0.2, help="Test split ratio")
    
    return parser.parse_args()

def load_manual_data(data_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    加载手动收集的数据
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        (observations, heights, actions)
    """
    print(f"Loading data from {data_path}...")
    
    data = np.load(data_path)
    observations = data["observations"]
    heights = data["heights"]
    actions = data["actions"]
    
    print(f"Data loaded:")
    print(f"  Observations shape: {observations.shape}")
    print(f"  Heights shape: {heights.shape}")
    print(f"  Actions shape: {actions.shape}")
    
    # 数据统计
    print(f"\nData statistics:")
    print(f"  Depth range: [{np.min(observations):.2f}, {np.max(observations):.2f}]")
    print(f"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}]")
    print(f"  Action ranges:")
    print(f"    Turn angle: [{np.min(actions[:, 0]):.2f}, {np.max(actions[:, 0]):.2f}]")
    print(f"    Pitch angle: [{np.min(actions[:, 1]):.2f}, {np.max(actions[:, 1]):.2f}]")
    print(f"    Speed: [{np.min(actions[:, 2]):.2f}, {np.max(actions[:, 2]):.2f}]")
    
    return observations, heights, actions

def split_data(observations: np.ndarray, heights: np.ndarray, actions: np.ndarray, 
               test_split: float = 0.2) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    分割训练和测试数据
    """
    n_samples = len(observations)
    n_test = int(n_samples * test_split)
    
    # 随机打乱数据
    indices = np.random.permutation(n_samples)
    
    test_indices = indices[:n_test]
    train_indices = indices[n_test:]
    
    train_obs = observations[train_indices]
    train_heights = heights[train_indices]
    train_actions = actions[train_indices]
    
    test_obs = observations[test_indices]
    test_heights = heights[test_indices]
    test_actions = actions[test_indices]
    
    print(f"Data split:")
    print(f"  Training samples: {len(train_obs)}")
    print(f"  Test samples: {len(test_obs)}")
    
    return train_obs, train_heights, train_actions, test_obs, test_heights, test_actions

def plot_training_results(loss_history: list, save_path: str):
    """
    绘制训练结果
    """
    plt.figure(figsize=(12, 4))
    
    # 损失曲线
    plt.subplot(1, 2, 1)
    plt.plot(loss_history)
    plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    # 最近100个epoch的损失
    plt.subplot(1, 2, 2)
    if len(loss_history) > 100:
        plt.plot(loss_history[-100:])
        plt.title('Training Loss (Last 100 Epochs)')
    else:
        plt.plot(loss_history)
        plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
    
    print(f"Training plots saved to {save_path}")

def evaluate_model(model: StudentModel, test_obs: np.ndarray, test_heights: np.ndarray, 
                  test_actions: np.ndarray, save_path: str):
    """
    评估模型性能
    """
    print("Evaluating model...")
    
    # 预测测试数据
    predictions = []
    for i in range(len(test_obs)):
        pred = model.predict(test_obs[i], test_heights[i])
        predictions.append(pred)
    
    predictions = np.array(predictions)
    
    # 计算误差
    errors = predictions - test_actions
    mae = np.mean(np.abs(errors), axis=0)
    rmse = np.sqrt(np.mean(errors**2, axis=0))
    
    print(f"Model evaluation results:")
    print(f"  Turn angle - MAE: {mae[0]:.3f}, RMSE: {rmse[0]:.3f}")
    print(f"  Pitch angle - MAE: {mae[1]:.3f}, RMSE: {rmse[1]:.3f}")
    print(f"  Speed - MAE: {mae[2]:.3f}, RMSE: {rmse[2]:.3f}")
    
    # 绘制预测对比图
    plt.figure(figsize=(15, 5))
    
    action_names = ['Turn Angle', 'Pitch Angle', 'Speed']
    for i in range(3):
        plt.subplot(1, 3, i+1)
        plt.scatter(test_actions[:, i], predictions[:, i], alpha=0.5)
        plt.plot([test_actions[:, i].min(), test_actions[:, i].max()], 
                [test_actions[:, i].min(), test_actions[:, i].max()], 'r--')
        plt.xlabel(f'True {action_names[i]}')
        plt.ylabel(f'Predicted {action_names[i]}')
        plt.title(f'{action_names[i]} Prediction')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
    
    print(f"Evaluation plots saved to {save_path}")

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 创建模型保存目录
    os.makedirs(args.model_dir, exist_ok=True)
    
    # 加载数据
    observations, heights, actions = load_manual_data(args.data_path)
    
    # 分割数据
    train_obs, train_heights, train_actions, test_obs, test_heights, test_actions = split_data(
        observations, heights, actions, args.test_split
    )
    
    # 创建模型
    print("Creating student model...")
    model = StudentModel()
    
    # 训练模型
    print(f"Training model for {args.epochs} epochs...")
    start_time = time.time()
    loss_history = model.train(train_obs, train_heights, train_actions, args.epochs, args.batch_size)
    training_time = time.time() - start_time
    
    print(f"Training completed in {training_time:.2f} seconds")
    print(f"Final loss: {loss_history[-1]:.6f}")
    
    # 保存模型
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(args.model_dir, f"student_model_manual_{timestamp}.pth")
    model.save(model_path)
    
    # 保存为最新模型
    latest_path = os.path.join(args.model_dir, "student_model_manual_latest.pth")
    model.save(latest_path)
    
    # 绘制训练结果
    loss_plot_path = os.path.join(args.model_dir, f"loss_curve_manual_{timestamp}.png")
    plot_training_results(loss_history, loss_plot_path)
    
    # 评估模型
    eval_plot_path = os.path.join(args.model_dir, f"prediction_comparison_manual_{timestamp}.png")
    evaluate_model(model, test_obs, test_heights, test_actions, eval_plot_path)
    
    print("Training completed successfully!")

if __name__ == "__main__":
    main()
