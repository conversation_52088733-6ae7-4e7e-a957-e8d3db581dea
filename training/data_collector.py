"""
数据收集模块
"""
import numpy as np
import os
import time
from typing import List, Tuple, Dict, Any
import pickle

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import DATA_COLLECTION_EPISODES, DATA_COLLECTION_STEPS_PER_EPISODE, DATA_DIR
from environment.drone_env import DroneEnvironment
from models.teacher_model import TeacherModel
from utils.visualization import plot_depth_image, plot_distance_data, plot_point_cloud

class DataCollector:
    """
    数据收集器
    """
    def __init__(self, env: DroneEnvironment, teacher: TeacherModel, data_dir: str = DATA_DIR,
                 save_episodes: bool = False, visualize: bool = False, visualize_interval: int = 50):
        """
        初始化数据收集器

        Args:
            env: 无人机环境
            teacher: 教师模型
            data_dir: 数据存储目录
            save_episodes: 是否保存每个回合的数据（默认为False，只保存最终数据）
            visualize: 是否可视化（默认为False）
            visualize_interval: 可视化间隔（默认为50步）
        """
        self.env = env
        self.teacher = teacher
        self.data_dir = data_dir
        self.save_episodes = save_episodes
        self.visualize = visualize
        self.visualize_interval = visualize_interval

        # 创建数据存储目录
        os.makedirs(data_dir, exist_ok=True)

        # 创建可视化目录
        if self.visualize:
            self.vis_dir = os.path.join(data_dir, "visualizations")
            os.makedirs(self.vis_dir, exist_ok=True)

        # 数据存储
        self.observations = []
        self.actions = []

    def collect_episode(self, max_steps: int = DATA_COLLECTION_STEPS_PER_EPISODE, episode_num: int = 0) -> Tuple[int, float]:
        """
        收集一个回合的数据

        Args:
            max_steps: 最大步数
            episode_num: 回合编号

        Returns:
            (步数, 总奖励)
        """
        # 重置环境
        observation = self.env.reset()

        total_reward = 0.0
        steps = 0

        for step in range(max_steps):
            # 使用get_safe_direction方法，它通常比predict方法更保守，避障效果更好
            action = self.teacher.get_safe_direction(observation)

            # 存储观测和动作
            self.observations.append(observation)
            self.actions.append([action])  # 转换为列表，以便后续处理

            # 可视化
            if self.visualize and step % self.visualize_interval == 0:
                # 创建回合目录
                episode_dir = os.path.join(self.vis_dir, f"episode_{episode_num}")
                os.makedirs(episode_dir, exist_ok=True)

                # 可视化深度图像
                if len(observation.shape) == 2:  # 深度图像
                    depth_img_path = os.path.join(episode_dir, f"depth_img_step_{step}.png")
                    plot_depth_image(observation, action, depth_img_path)

                    # 可视化距离数组（如果有）
                    if self.env.last_distance_array is not None:
                        distance_array_path = os.path.join(episode_dir, f"distance_array_step_{step}.png")
                        plot_distance_data(self.env.last_distance_array, action, distance_array_path)

                    # 可视化点云（如果有）
                    if self.env.last_point_cloud is not None:
                        point_cloud_path = os.path.join(episode_dir, f"point_cloud_step_{step}.png")
                        plot_point_cloud(self.env.last_point_cloud, point_cloud_path)
                else:  # 距离数组
                    distance_array_path = os.path.join(episode_dir, f"distance_array_step_{step}.png")
                    plot_distance_data(observation, action, distance_array_path)

            # 执行动作
            next_observation, reward, done, info = self.env.step(action)

            # 更新观测和奖励
            observation = next_observation
            total_reward += reward
            steps += 1

            # 打印进度
            if step % 10 == 0:
                print(f"Step {step}/{max_steps}, Action: {action:.2f}, Reward: {reward:.2f}")

            # 如果回合结束，退出循环
            if done:
                print(f"Episode ended after {step + 1} steps due to collision")
                break

        return steps, total_reward

    def collect_data(self, episodes: int = DATA_COLLECTION_EPISODES, max_steps: int = DATA_COLLECTION_STEPS_PER_EPISODE) -> Tuple[np.ndarray, np.ndarray]:
        """
        收集多个回合的数据

        Args:
            episodes: 回合数
            max_steps: 每个回合的最大步数

        Returns:
            (observations, actions)
        """
        print(f"Starting data collection: {episodes} episodes, {max_steps} steps per episode")

        # 清空数据
        self.observations = []
        self.actions = []

        # 用于累积所有回合的数据
        all_observations = []
        all_actions = []

        # 收集数据
        for episode in range(episodes):
            print(f"\nEpisode {episode + 1}/{episodes}")

            # 清空当前回合的数据
            self.observations = []
            self.actions = []

            steps, total_reward = self.collect_episode(max_steps, episode)
            print(f"Episode {episode + 1} completed: {steps} steps, total reward: {total_reward:.2f}")

            # 如果设置了保存每个回合的数据，则保存
            if self.save_episodes:
                self.save_data(f"episode_{episode + 1}")

            # 累积数据
            all_observations.extend(self.observations)
            all_actions.extend(self.actions)

        # 更新为所有回合的累积数据
        self.observations = all_observations
        self.actions = all_actions

        # 转换为numpy数组
        observations = np.array(self.observations)
        actions = np.array(self.actions)

        print(f"Data collection completed: {len(observations)} samples collected")
        print(f"Observations shape: {observations.shape}, Actions shape: {actions.shape}")

        # 保存数据统计信息
        if len(observations) > 0:
            if len(observations.shape) == 3:  # 深度图像
                print(f"Depth image statistics:")
                print(f"  Min depth: {np.min(observations):.2f}")
                print(f"  Max depth: {np.max(observations):.2f}")
                print(f"  Mean depth: {np.mean(observations):.2f}")

            print(f"Actions statistics:")
            print(f"  Min action: {np.min(actions):.2f}")
            print(f"  Max action: {np.max(actions):.2f}")
            print(f"  Mean action: {np.mean(actions):.2f}")
            print(f"  Action distribution:")
            # 计算动作分布
            action_bins = np.linspace(-180, 180, 9)
            action_hist, _ = np.histogram(actions, bins=action_bins)
            for i in range(len(action_bins) - 1):
                print(f"    {action_bins[i]:.1f} to {action_bins[i+1]:.1f}: {action_hist[i]} ({action_hist[i]/len(actions)*100:.1f}%)")

        return observations, actions

    def save_data(self, suffix: str = "") -> None:
        """
        保存数据

        Args:
            suffix: 文件名后缀
        """
        # 生成文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"depth_data_{timestamp}"
        if suffix:
            filename += f"_{suffix}"
        filename += ".npz"

        # 保存数据
        data = {
            "observations": np.array(self.observations),
            "actions": np.array(self.actions)
        }

        filepath = os.path.join(self.data_dir, filename)
        np.savez_compressed(filepath, **data)

        print(f"Data saved to {filepath}")

        # 保存数据统计信息
        observations = np.array(self.observations)
        actions = np.array(self.actions)

        stats_filename = f"depth_data_{timestamp}"
        if suffix:
            stats_filename += f"_{suffix}"
        stats_filename += "_stats.txt"
        stats_filepath = os.path.join(self.data_dir, stats_filename)

        with open(stats_filepath, "w") as f:
            f.write(f"Data statistics:\n")
            f.write(f"Samples: {len(observations)}\n")
            f.write(f"Observations shape: {observations.shape}\n")
            f.write(f"Actions shape: {actions.shape}\n\n")

            if len(observations) > 0:
                if len(observations.shape) == 3:  # 深度图像
                    f.write(f"Depth image statistics:\n")
                    f.write(f"  Min depth: {np.min(observations):.2f}\n")
                    f.write(f"  Max depth: {np.max(observations):.2f}\n")
                    f.write(f"  Mean depth: {np.mean(observations):.2f}\n\n")

                f.write(f"Actions statistics:\n")
                f.write(f"  Min action: {np.min(actions):.2f}\n")
                f.write(f"  Max action: {np.max(actions):.2f}\n")
                f.write(f"  Mean action: {np.mean(actions):.2f}\n")
                f.write(f"  Action distribution:\n")
                # 计算动作分布
                action_bins = np.linspace(-180, 180, 9)
                action_hist, _ = np.histogram(actions, bins=action_bins)
                for i in range(len(action_bins) - 1):
                    f.write(f"    {action_bins[i]:.1f} to {action_bins[i+1]:.1f}: {action_hist[i]} ({action_hist[i]/len(actions)*100:.1f}%)\n")

        print(f"Data statistics saved to {stats_filepath}")

    def load_data(self, filepath: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载数据

        Args:
            filepath: 文件路径

        Returns:
            (observations, actions)
        """
        # 检查文件扩展名
        if filepath.endswith('.pkl'):
            # 旧格式（pickle）
            with open(filepath, "rb") as f:
                data = pickle.load(f)
        else:
            # 新格式（npz）
            data = np.load(filepath)

        observations = data["observations"]
        actions = data["actions"]

        print(f"Data loaded from {filepath}: {len(observations)} samples")
        print(f"Observations shape: {observations.shape}, Actions shape: {actions.shape}")

        return observations, actions
