# 快速开始指南 - SSH远程控制版本

## 🚀 快速开始

### 1. 确保AirSim正在运行
确保您的AirSim仿真环境已经启动并可以连接。

### 2. 测试实时手动控制（推荐）
```bash
python3 simple_realtime_control.py --test_mode
```

### 3. 开始收集数据
```bash
python3 simple_realtime_control.py --max_samples 1000
```

### 4. 备选：交互式控制（需要回车确认）
```bash
python3 simple_manual_control.py --test_mode
```

## 📋 控制命令

### 基本控制
- `a` - 左转10度
- `d` - 右转10度
- `w` - 向上俯仰10度
- `s` - 向下俯仰10度
- `u` - 加速0.2m/s
- `j` - 减速0.2m/s

### 执行和管理
- `go` - 执行当前设置的动作
- `collect` - 切换数据收集模式（仅在非测试模式下）
- `reset` - 重置所有控制参数
- `status` - 显示当前状态
- `help` - 显示帮助信息
- `quit` - 退出程序

## 🎮 使用流程

### 实时控制流程（推荐）
1. 启动程序：`python3 simple_realtime_control.py --test_mode`
2. 直接按键控制：
   - 按 `a` 立即左转10度
   - 按 `d` 立即右转10度
   - 按 `u` 立即加速0.2m/s
   - 按 `w` 立即向上俯仰10度
3. 无人机会保持当前速度持续飞行
4. 按 `q` 退出

### 数据收集流程
1. 启动程序：`python3 simple_realtime_control.py --max_samples 1000`
2. 按空格键开启数据收集
3. 直接按键控制并自动收集数据
4. 按 `q` 退出并自动保存数据

## 📊 示例操作序列

### 基本飞行测试
```
> status          # 查看当前状态
> w               # 设置向上俯仰10度
> d               # 设置右转10度
> u               # 设置加速到1.2m/s
> go              # 执行动作
> status          # 查看执行后状态
```

### 数据收集示例
```
> collect         # 开启数据收集
> w               # 向上俯仰
> go              # 执行并收集数据
> a               # 左转
> go              # 执行并收集数据
> s               # 向下俯仰
> go              # 执行并收集数据
> quit            # 退出并保存数据
```

## 🔧 高级功能

### 自动步进模式
如果您想要自动定期执行动作，可以使用：
```bash
python3 collect_data_manual_api.py --auto_step_interval 2.0
```
这会每2秒自动执行一次当前设置的动作。

### 断点续存
```bash
# 从现有数据文件继续收集
python3 simple_manual_control.py --resume_from data/manual_data_20231201_120000_final.npz
```

## 📈 训练模型

收集数据后，训练新模型：
```bash
python3 train_model_manual.py --data_path data/manual_data_YYYYMMDD_HHMMSS_final.npz --epochs 100
```

运行训练好的模型：
```bash
python3 run_student_manual.py --model_path saved_models/student_model_manual_latest.pth --visualize
```

## ⚠️ 注意事项

1. **碰撞处理**：发生碰撞时系统会自动重置无人机位置
2. **参数限制**：
   - 俯仰角限制在±30度
   - 速度限制在0.2-3.0 m/s
   - 转向角无限制（会累加到当前偏航角）
3. **数据质量**：建议在不同场景下收集多样化的数据
4. **存储空间**：深度图像数据较大，注意磁盘空间

## 🐛 故障排除

### 连接问题
- 确保AirSim正在运行
- 检查IP和端口配置（默认127.0.0.1:41451）

### 控制异常
- 输入 `reset` 重置控制状态
- 输入 `status` 检查当前状态
- 重启程序

### 数据保存失败
- 检查data目录权限
- 确保有足够磁盘空间

## 📁 文件说明

- `simple_manual_control.py` - 简单交互式控制（推荐）
- `collect_data_manual_api.py` - 高级API控制（带自动步进）
- `manual_control_api.py` - 控制模块
- `train_model_manual.py` - 训练脚本
- `run_student_manual.py` - 运行脚本

现在您可以开始使用SSH远程控制收集数据了！
