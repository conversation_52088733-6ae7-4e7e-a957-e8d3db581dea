# 快速开始指南

## 手动控制数据收集

### 1. 启动AirSim
确保AirSim已经启动并连接。

### 2. 测试控制（推荐先测试）
```bash
python ssh_realtime_control.py --test_mode
```

### 3. 开始数据收集
```bash
python ssh_realtime_control.py --max_samples 5000
```

### 4. 控制说明
- **A/D**: 左转/右转
- **W/S**: 向上俯仰/向下俯仰  
- **U/J**: 加速/减速
- **空格**: 切换数据收集模式
- **Q或ESC**: 退出

### 5. 实时状态显示
程序会显示当前状态：
```
俯仰: 0.0° | 速度: 1.0m/s | 数据收集: 开启 | 样本: 123
```

### 6. 数据保存
程序退出时会自动保存数据到 `data/` 目录，文件名格式：
```
manual_data_YYYYMMDD_HHMMSS_final.npz
```

## 注意事项

1. **SSH环境**: 此脚本专为SSH环境设计，无需GUI
2. **实时响应**: 按键立即生效，无需按Enter
3. **碰撞重置**: 发生碰撞会自动重置环境
4. **断点续存**: 支持从现有数据文件继续收集

## 故障排除

### 连接问题
如果无法连接AirSim，检查：
- AirSim是否正在运行
- 端口41451是否可用

### 按键无响应
如果按键无响应：
- 确保终端窗口处于活动状态
- 尝试按Q退出后重新启动

### 数据收集问题
如果数据收集有问题：
- 确保data目录存在且可写
- 检查磁盘空间是否充足
