"""
基于API的手动控制数据收集脚本（适用于SSH远程连接）
"""
import os
import time
import argparse
import signal
import sys
import numpy as np
import threading

from environment.drone_env import DroneEnvironment
from manual_control_api import APIManualController, ManualDataCollector
from config import DATA_DIR

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="API-based manual data collection")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--resume_from", type=str, help="Resume from existing data file")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=10000, help="Maximum samples to collect")
    parser.add_argument("--auto_step_interval", type=float, default=1.0, help="Auto step interval in seconds")
    
    return parser.parse_args()

def signal_handler(signum, frame):
    """
    信号处理器，用于优雅退出
    """
    print("\n收到退出信号，正在保存数据...")
    sys.exit(0)

class AutoStepController:
    """
    自动步进控制器，定期执行动作
    """
    def __init__(self, env: DroneEnvironment, controller: APIManualController, 
                 data_collector: ManualDataCollector, interval: float = 1.0):
        self.env = env
        self.controller = controller
        self.data_collector = data_collector
        self.interval = interval
        self.running = False
        self.thread = None
        
    def start(self):
        """启动自动步进"""
        self.running = True
        self.thread = threading.Thread(target=self._auto_step_loop)
        self.thread.daemon = True
        self.thread.start()
        print(f"自动步进已启动，间隔: {self.interval}秒")
        
    def stop(self):
        """停止自动步进"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("自动步进已停止")
        
    def _auto_step_loop(self):
        """自动步进循环"""
        sample_count = 0
        last_collision_time = 0
        
        while self.running and self.controller.running:
            try:
                # 获取当前动作
                yaw_delta, pitch, speed = self.controller.get_current_action()
                
                # 获取当前高度
                drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
                current_height = -drone_state.kinematics_estimated.position.z_val
                
                # 获取当前观测
                observation = self.env.get_observation()
                
                # 执行动作
                next_observation, reward, done, info = self.env.step_manual(yaw_delta, pitch, speed)
                
                # 如果正在收集数据
                if self.controller.is_collecting_data() and self.data_collector:
                    # 添加样本
                    self.data_collector.add_sample(observation, current_height, (yaw_delta, pitch, speed))
                    sample_count += 1
                    
                    if sample_count % 50 == 0:
                        print(f"已收集 {sample_count} 个样本")
                
                # 如果发生碰撞
                if done:
                    current_time = time.time()
                    # 防止频繁重置（至少间隔5秒）
                    if current_time - last_collision_time > 5.0:
                        print("检测到碰撞，正在重置...")
                        
                        # 重置环境
                        self.env.reset()
                        
                        # 重置控制器状态
                        self.controller.reset_after_collision()
                        
                        last_collision_time = current_time
                    else:
                        print("碰撞检测被忽略（间隔太短）")
                
                # 等待指定间隔
                time.sleep(self.interval)
                
            except Exception as e:
                print(f"自动步进循环中发生错误: {e}")
                time.sleep(1.0)

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建保存目录
    os.makedirs(args.data_dir, exist_ok=True)
    
    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()
    
    # 创建数据收集器（如果不是测试模式）
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir, args.resume_from)
    
    # 创建手动控制器
    controller = APIManualController(env, data_collector)
    
    # 创建自动步进控制器
    auto_stepper = AutoStepController(env, controller, data_collector, args.auto_step_interval)
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        observation = env.reset()
        
        print(f"\n{'='*60}")
        print("基于API的手动控制数据收集已启动")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"自动步进间隔: {args.auto_step_interval}秒")
        print(f"{'='*60}")
        
        # 启动自动步进
        auto_stepper.start()
        
        # 启动手动控制（这会阻塞直到用户退出）
        controller.start_control()
        
    except KeyboardInterrupt:
        print("\n用户中断...")
        
    except Exception as e:
        print(f"发生错误: {e}")
        
    finally:
        # 停止自动步进
        auto_stepper.stop()
        
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        
        print("程序已退出")

if __name__ == "__main__":
    main()
