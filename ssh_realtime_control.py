"""
适用于SSH环境的实时手动控制脚本
使用threading和select实现真正的实时控制
"""
import os
import time
import argparse
import threading
import select
import sys
import termios
import tty
from collections import defaultdict

from environment.drone_env import DroneEnvironment
from manual_control_api import ManualDataCollector
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MANUAL_CONTROL_FREQUENCY, MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE, 
    FORWARD_SPEED, DATA_DIR
)

class SSHRealtimeController:
    """
    适用于SSH环境的实时控制器
    """
    def __init__(self, env: DroneEnvironment, data_collector=None, test_mode=False):
        self.env = env
        self.data_collector = data_collector
        self.test_mode = test_mode
        
        # 控制状态
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        self.collecting_data = False
        self.running = False
        
        # 按键状态
        self.pressed_keys = set()
        self.key_lock = threading.Lock()
        
        # 统计
        self.sample_count = 0
        
        # 控制参数
        self.control_frequency = MANUAL_CONTROL_FREQUENCY  # 4Hz
        self.control_interval = 1.0 / self.control_frequency
        
        # 终端设置
        self.old_settings = None
        
    def setup_terminal(self):
        """设置终端为原始模式"""
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
    def restore_terminal(self):
        """恢复终端设置"""
        if self.old_settings:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
    
    def key_listener(self):
        """按键监听线程"""
        while self.running:
            try:
                # 使用select检查是否有输入
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    key = sys.stdin.read(1)
                    
                    with self.key_lock:
                        if key == '\x1b':  # ESC键
                            # 检查是否是方向键
                            if select.select([sys.stdin], [], [], 0.1)[0]:
                                key += sys.stdin.read(2)
                                if key == '\x1b[A':  # UP
                                    self.pressed_keys.add('UP')
                                elif key == '\x1b[B':  # DOWN
                                    self.pressed_keys.add('DOWN')
                                elif key == '\x1b[C':  # RIGHT
                                    self.pressed_keys.add('RIGHT')
                                elif key == '\x1b[D':  # LEFT
                                    self.pressed_keys.add('LEFT')
                            else:
                                # 纯ESC键，退出
                                self.running = False
                                break
                        elif key == ' ':  # 空格
                            self.pressed_keys.add('SPACE')
                        elif key.lower() in ['w', 'a', 's', 'd']:
                            self.pressed_keys.add(key.upper())
                        elif key == 'q':
                            self.running = False
                            break
                            
            except Exception as e:
                print(f"按键监听错误: {e}")
                break
    
    def control_loop(self):
        """控制循环线程"""
        last_control_time = time.time()
        last_status_time = time.time()
        
        while self.running:
            current_time = time.time()
            
            # 控制频率限制
            if current_time - last_control_time >= self.control_interval:
                self._process_controls()
                last_control_time = current_time
            
            # 状态显示频率限制（每秒一次）
            if current_time - last_status_time >= 1.0:
                self._display_status()
                last_status_time = current_time
            
            time.sleep(0.01)  # 10ms循环
    
    def _process_controls(self):
        """处理控制输入"""
        with self.key_lock:
            current_keys = self.pressed_keys.copy()
            self.pressed_keys.clear()  # 清空按键状态
        
        if not current_keys:
            return
        
        # 处理特殊按键
        if 'SPACE' in current_keys:
            self._toggle_data_collection()
        
        # 计算控制增量
        yaw_delta = 0
        pitch_changed = False
        speed_changed = False
        
        # 转向控制
        if 'A' in current_keys or 'LEFT' in current_keys:
            yaw_delta = -MANUAL_CONTROL_TURN_STEP
            print("左转")
        elif 'D' in current_keys or 'RIGHT' in current_keys:
            yaw_delta = MANUAL_CONTROL_TURN_STEP
            print("右转")
        
        # 俯仰控制
        if 'W' in current_keys:
            self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
            pitch_changed = True
            print(f"向上俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        elif 'S' in current_keys:
            self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
            pitch_changed = True
            print(f"向下俯仰，当前俯仰角: {self.current_pitch:.1f}°")
        
        # 速度控制
        if 'UP' in current_keys:
            self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
            speed_changed = True
            print(f"加速，当前速度: {self.current_speed:.1f}m/s")
        elif 'DOWN' in current_keys:
            self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
            speed_changed = True
            print(f"减速，当前速度: {self.current_speed:.1f}m/s")
        
        # 如果有任何控制输入，执行动作
        if yaw_delta != 0 or pitch_changed or speed_changed:
            self._execute_action(yaw_delta)
    
    def _execute_action(self, yaw_delta):
        """执行动作"""
        try:
            # 获取当前观测和高度
            observation = self.env.get_observation()
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val
            
            # 执行动作
            next_observation, reward, done, info = self.env.step_manual(yaw_delta, self.current_pitch, self.current_speed)
            
            # 如果正在收集数据
            if self.collecting_data and self.data_collector and not self.test_mode:
                self.data_collector.add_sample(observation, current_height, (yaw_delta, self.current_pitch, self.current_speed))
                self.sample_count += 1
                if self.sample_count % 50 == 0:
                    print(f"已收集 {self.sample_count} 个样本")
            
            # 检查碰撞
            if done:
                print("检测到碰撞！正在重置...")
                self.env.reset()
                self._reset_state()
                
        except Exception as e:
            print(f"执行动作时出错: {e}")
    
    def _reset_state(self):
        """重置控制状态"""
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")
    
    def _toggle_data_collection(self):
        """切换数据收集模式"""
        if self.test_mode:
            print("测试模式下无法收集数据")
            return
            
        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")
    
    def _display_status(self):
        """显示状态信息"""
        status = f"俯仰: {self.current_pitch:.1f}° | 速度: {self.current_speed:.1f}m/s | 数据收集: {'开启' if self.collecting_data else '关闭'} | 样本: {self.sample_count}"
        print(f"\r{status}", end='', flush=True)
    
    def start(self):
        """启动控制"""
        self.running = True
        
        print("\nSSH实时控制已启动!")
        print("=" * 50)
        print("控制说明:")
        print("  A/D: 左转/右转")
        print("  W/S: 向上俯仰/向下俯仰")
        print("  ↑/↓: 加速/减速")
        print("  空格: 切换数据收集模式")
        print("  ESC或Q: 退出")
        print("=" * 50)
        print("按键立即生效，无需按Enter确认")
        print("=" * 50)
        
        try:
            # 设置终端
            self.setup_terminal()
            
            # 启动线程
            key_thread = threading.Thread(target=self.key_listener, daemon=True)
            control_thread = threading.Thread(target=self.control_loop, daemon=True)
            
            key_thread.start()
            control_thread.start()
            
            # 主线程等待
            while self.running:
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n用户中断...")
        finally:
            self.running = False
            self.restore_terminal()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="SSH-compatible realtime manual control")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=5000, help="Maximum samples to collect")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建环境
    print("正在连接AirSim...")
    env = DroneEnvironment()
    
    # 创建数据收集器
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir)
    
    # 创建控制器
    controller = SSHRealtimeController(env, data_collector, args.test_mode)
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        env.reset()
        
        print(f"\n{'='*60}")
        print("SSH实时手动控制")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*60}")
        
        # 启动控制
        controller.start()
        
    except KeyboardInterrupt:
        print("\n用户中断...")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        print("程序已退出")

if __name__ == "__main__":
    main()
