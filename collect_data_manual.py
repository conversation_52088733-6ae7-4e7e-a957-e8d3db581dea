"""
手动控制数据收集脚本
"""
import os
import time
import argparse
import signal
import sys
import numpy as np

from environment.drone_env import DroneEnvironment
from manual_control import ManualController, ManualDataCollector
from config import DATA_DIR, TAKEOFF_HEIGHT

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Manual data collection with keyboard control")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--resume_from", type=str, help="Resume from existing data file")
    parser.add_argument("--test_mode", action="store_true", help="Test mode (no data collection)")
    parser.add_argument("--max_samples", type=int, default=10000, help="Maximum samples to collect")
    
    return parser.parse_args()

def signal_handler(signum, frame):
    """
    信号处理器，用于优雅退出
    """
    print("\n收到退出信号，正在保存数据...")
    sys.exit(0)

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建保存目录
    os.makedirs(args.data_dir, exist_ok=True)
    
    # 创建环境
    env = DroneEnvironment()
    
    # 创建数据收集器（如果不是测试模式）
    data_collector = None
    if not args.test_mode:
        data_collector = ManualDataCollector(args.data_dir, args.resume_from)
    
    # 创建手动控制器
    controller = ManualController(env, data_collector)
    
    try:
        # 重置环境并起飞
        print("正在重置环境并起飞...")
        observation = env.reset()
        
        # 启动手动控制
        controller.start_control()
        
        print(f"\n{'='*50}")
        print("手动控制数据收集已启动")
        if args.test_mode:
            print("当前模式: 测试模式（不收集数据）")
        else:
            print("当前模式: 数据收集模式")
            print(f"最大样本数: {args.max_samples}")
        print(f"{'='*50}")
        
        sample_count = 0
        last_collision_time = 0
        
        while controller.running:
            try:
                # 获取当前动作
                yaw_delta, pitch, speed = controller.get_current_action()
                
                # 获取当前高度
                drone_state = env.client.getMultirotorState(vehicle_name=env.drone_name)
                current_height = -drone_state.kinematics_estimated.position.z_val
                
                # 如果有动作变化或正在收集数据，执行动作
                if abs(yaw_delta) > 0.1 or controller.is_collecting_data():
                    # 执行动作（这里我们需要修改环境以支持新的动作空间）
                    next_observation, reward, done, info = env.step_manual(yaw_delta, pitch, speed)
                    
                    # 如果正在收集数据且不是测试模式
                    if controller.is_collecting_data() and not args.test_mode and data_collector:
                        # 添加样本
                        data_collector.add_sample(observation, current_height, (yaw_delta, pitch, speed))
                        sample_count += 1
                        
                        if sample_count % 100 == 0:
                            print(f"已收集 {sample_count} 个样本")
                            
                        # 检查是否达到最大样本数
                        if sample_count >= args.max_samples:
                            print(f"已达到最大样本数 {args.max_samples}，停止收集")
                            break
                    
                    # 更新观测
                    observation = next_observation
                    
                    # 如果发生碰撞
                    if done:
                        current_time = time.time()
                        # 防止频繁重置（至少间隔5秒）
                        if current_time - last_collision_time > 5.0:
                            print("检测到碰撞，正在重置...")
                            
                            # 重置环境
                            observation = env.reset()
                            
                            # 重置控制器状态
                            controller.reset_after_collision()
                            
                            last_collision_time = current_time
                        else:
                            print("碰撞检测被忽略（间隔太短）")
                
                # 显示状态信息
                if sample_count % 50 == 0 and sample_count > 0:
                    mode_str = "收集中" if controller.is_collecting_data() else "暂停"
                    print(f"状态: {mode_str}, 样本数: {sample_count}, 高度: {current_height:.2f}m")
                
                # 短暂休眠
                time.sleep(0.05)  # 20Hz控制频率
                
            except Exception as e:
                print(f"控制循环中发生错误: {e}")
                time.sleep(0.1)
        
    except KeyboardInterrupt:
        print("\n用户中断...")
        
    except Exception as e:
        print(f"发生错误: {e}")
        
    finally:
        # 停止控制
        controller.stop_control()
        
        # 保存数据
        if not args.test_mode and data_collector and len(data_collector.observations) > 0:
            print(f"\n正在保存数据... (共 {len(data_collector.observations)} 个样本)")
            saved_path = data_collector.save_data("final")
            print(f"数据已保存到: {saved_path}")
        
        # 关闭环境
        env.close()
        
        print("程序已退出")

if __name__ == "__main__":
    main()
