"""
学生模型（神经网络）
"""
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import os
from typing import Tuple, List, Dict, Any

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    INPUT_CHANNELS, INPUT_WIDTH, INPUT_HEIGHT, ADDITIONAL_INPUT_SIZE,
    CONV_FILTERS, CONV_KERNEL_SIZES, CONV_STRIDES,
    FC_SIZES, OUTPUT_SIZE, LEARNING_RATE, BATCH_SIZE, EPOCHS, MODEL_DIR
)

class ObstacleAvoidanceCNN(nn.Module):
    """
    基于CNN+FC的避障神经网络模型，支持深度图像和高度输入
    """
    def __init__(self,
                 input_channels: int = INPUT_CHANNELS,
                 input_width: int = INPUT_WIDTH,
                 input_height: int = INPUT_HEIGHT,
                 additional_input_size: int = ADDITIONAL_INPUT_SIZE,
                 conv_filters: List[int] = CONV_FILTERS,
                 conv_kernel_sizes: List[int] = CONV_KERNEL_SIZES,
                 conv_strides: List[int] = CONV_STRIDES,
                 fc_sizes: List[int] = FC_SIZES,
                 output_size: int = OUTPUT_SIZE):
        """
        初始化神经网络

        Args:
            input_channels: 输入通道数
            input_width: 输入宽度
            input_height: 输入高度
            additional_input_size: 额外输入大小（高度值）
            conv_filters: 卷积层滤波器数量
            conv_kernel_sizes: 卷积核大小
            conv_strides: 卷积步长
            fc_sizes: 全连接层大小
            output_size: 输出大小（转向角、俯仰角、前进速度）
        """
        super(ObstacleAvoidanceCNN, self).__init__()

        # 卷积层
        self.conv_layers = nn.ModuleList()
        in_channels = input_channels

        # 计算卷积后的特征图大小
        feature_width = input_width
        feature_height = input_height

        for i in range(len(conv_filters)):
            out_channels = conv_filters[i]
            kernel_size = conv_kernel_sizes[i]
            stride = conv_strides[i]

            self.conv_layers.append(nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding=kernel_size//2))
            self.conv_layers.append(nn.ReLU())
            self.conv_layers.append(nn.BatchNorm2d(out_channels))

            # 更新特征图大小
            feature_width = (feature_width + 2*(kernel_size//2) - kernel_size) // stride + 1
            feature_height = (feature_height + 2*(kernel_size//2) - kernel_size) // stride + 1

            in_channels = out_channels

        # 计算展平后的特征数量
        flattened_size = feature_width * feature_height * conv_filters[-1]

        # 全连接层
        self.fc_layers = nn.ModuleList()
        # 第一个全连接层需要合并CNN特征和额外输入
        in_features = flattened_size + additional_input_size

        for fc_size in fc_sizes:
            self.fc_layers.append(nn.Linear(in_features, fc_size))
            self.fc_layers.append(nn.ReLU())
            self.fc_layers.append(nn.Dropout(0.2))
            in_features = fc_size

        # 输出层
        self.output_layer = nn.Linear(in_features, output_size)

        # 保存额外输入大小
        self.additional_input_size = additional_input_size

        # 保存特征图大小，用于前向传播
        self.feature_width = feature_width
        self.feature_height = feature_height
        self.final_conv_filters = conv_filters[-1]

    def forward(self, depth_img: torch.Tensor, additional_input: torch.Tensor = None) -> torch.Tensor:
        """
        前向传播

        Args:
            depth_img: 深度图像张量 [batch_size, channels, height, width]
            additional_input: 额外输入张量 [batch_size, additional_input_size]

        Returns:
            输出张量 [batch_size, output_size]
        """
        # 卷积层处理深度图像
        x = depth_img
        for layer in self.conv_layers:
            x = layer(x)

        # 展平CNN特征
        x = x.view(x.size(0), -1)

        # 如果有额外输入，合并特征
        if additional_input is not None:
            x = torch.cat([x, additional_input], dim=1)
        elif self.additional_input_size > 0:
            # 如果模型期望额外输入但没有提供，用零填充
            batch_size = x.size(0)
            zeros = torch.zeros(batch_size, self.additional_input_size, device=x.device)
            x = torch.cat([x, zeros], dim=1)

        # 全连接层
        for layer in self.fc_layers:
            x = layer(x)

        # 输出层
        x = self.output_layer(x)

        return x

class DepthDataset(Dataset):
    """
    深度图像和高度数据集
    """
    def __init__(self, observations: np.ndarray, heights: np.ndarray, actions: np.ndarray):
        """
        初始化数据集

        Args:
            observations: 观测数据（深度图像）
            heights: 高度数据
            actions: 动作数据
        """
        # 确保深度图像形状正确
        if len(observations.shape) == 3:
            # 如果已经是3D数组 [samples, height, width]
            self.observations = observations
        elif len(observations.shape) == 4 and observations.shape[1] == 1:
            # 如果是4D数组 [samples, channels, height, width]
            self.observations = observations[:, 0, :, :]
        else:
            raise ValueError(f"Unexpected observation shape: {observations.shape}")

        # 转换为PyTorch张量
        # 添加通道维度 [samples, height, width] -> [samples, 1, height, width]
        self.observations = torch.FloatTensor(self.observations).unsqueeze(1)
        self.heights = torch.FloatTensor(heights).unsqueeze(1)  # [samples, 1]
        self.actions = torch.FloatTensor(actions)

    def __len__(self) -> int:
        return len(self.observations)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return self.observations[idx], self.heights[idx], self.actions[idx]

class StudentModel:
    """
    学生模型
    """
    def __init__(self,
                 input_channels: int = INPUT_CHANNELS,
                 input_width: int = INPUT_WIDTH,
                 input_height: int = INPUT_HEIGHT,
                 additional_input_size: int = ADDITIONAL_INPUT_SIZE,
                 conv_filters: List[int] = CONV_FILTERS,
                 conv_kernel_sizes: List[int] = CONV_KERNEL_SIZES,
                 conv_strides: List[int] = CONV_STRIDES,
                 fc_sizes: List[int] = FC_SIZES,
                 output_size: int = OUTPUT_SIZE):
        """
        初始化学生模型

        Args:
            input_channels: 输入通道数
            input_width: 输入宽度
            input_height: 输入高度
            additional_input_size: 额外输入大小
            conv_filters: 卷积层滤波器数量
            conv_kernel_sizes: 卷积核大小
            conv_strides: 卷积步长
            fc_sizes: 全连接层大小
            output_size: 输出大小
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = ObstacleAvoidanceCNN(
            input_channels, input_width, input_height, additional_input_size,
            conv_filters, conv_kernel_sizes, conv_strides,
            fc_sizes, output_size
        ).to(self.device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=LEARNING_RATE)
        self.criterion = nn.MSELoss()

        # 创建模型保存目录
        os.makedirs(MODEL_DIR, exist_ok=True)

        # 打印模型结构
        print(f"Model created on device: {self.device}")
        print(f"Input shape: [{input_channels}, {input_height}, {input_width}]")

        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"Total parameters: {total_params:,}")

    def train(self, observations: np.ndarray, heights: np.ndarray, actions: np.ndarray, epochs: int = EPOCHS, batch_size: int = BATCH_SIZE) -> List[float]:
        """
        训练模型

        Args:
            observations: 观测数据（深度图像）
            heights: 高度数据
            actions: 动作数据
            epochs: 训练轮数
            batch_size: 批次大小

        Returns:
            训练损失历史
        """
        # 创建数据集和数据加载器
        dataset = DepthDataset(observations, heights, actions)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        loss_history = []

        for epoch in range(epochs):
            epoch_loss = 0.0
            for batch_observations, batch_heights, batch_actions in dataloader:
                batch_observations = batch_observations.to(self.device)
                batch_heights = batch_heights.to(self.device)
                batch_actions = batch_actions.to(self.device)

                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(batch_observations, batch_heights)
                loss = self.criterion(outputs, batch_actions)

                # 反向传播
                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()

            # 记录损失
            avg_loss = epoch_loss / len(dataloader)
            loss_history.append(avg_loss)

            # 打印进度
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.6f}")

        return loss_history

    def predict(self, observation: np.ndarray, height: float = None) -> Tuple[float, float, float]:
        """
        预测动作

        Args:
            observation: 观测数据（深度图像）
            height: 当前高度

        Returns:
            预测的动作 (转向角, 俯仰角, 前进速度)
        """
        self.model.eval()
        with torch.no_grad():
            # 确保输入形状正确
            if len(observation.shape) == 2:
                # 单张深度图像 [height, width]
                # 转换为 [1, 1, height, width]
                observation_tensor = torch.FloatTensor(observation).unsqueeze(0).unsqueeze(0).to(self.device)
            elif len(observation.shape) == 3 and observation.shape[0] == 1:
                # 已经有通道维度 [1, height, width]
                observation_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
            else:
                raise ValueError(f"Unexpected observation shape: {observation.shape}")

            # 准备高度输入
            height_tensor = None
            if height is not None:
                height_tensor = torch.FloatTensor([[height]]).to(self.device)

            output = self.model(observation_tensor, height_tensor)

            # 返回三个输出值
            if output.shape[1] == 3:
                return output[0, 0].item(), output[0, 1].item(), output[0, 2].item()
            else:
                # 兼容旧模型（只有一个输出）
                return output[0, 0].item(), 0.0, 1.0

    def save(self, path: str = None) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        if path is None:
            path = os.path.join(MODEL_DIR, "student_model.pth")

        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'model_config': {
                'input_channels': INPUT_CHANNELS,
                'input_width': INPUT_WIDTH,
                'input_height': INPUT_HEIGHT,
                'additional_input_size': ADDITIONAL_INPUT_SIZE,
                'conv_filters': CONV_FILTERS,
                'conv_kernel_sizes': CONV_KERNEL_SIZES,
                'conv_strides': CONV_STRIDES,
                'fc_sizes': FC_SIZES,
                'output_size': OUTPUT_SIZE
            }
        }, path)

        print(f"Model saved to {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        checkpoint = torch.load(path, map_location=self.device)

        # 检查是否有模型配置
        if 'model_config' in checkpoint:
            config = checkpoint['model_config']
            # 使用保存的配置重新创建模型
            additional_input_size = config.get('additional_input_size', 0)  # 兼容旧模型
            self.model = ObstacleAvoidanceCNN(
                config['input_channels'],
                config['input_width'],
                config['input_height'],
                additional_input_size,
                config['conv_filters'],
                config['conv_kernel_sizes'],
                config['conv_strides'],
                config['fc_sizes'],
                config['output_size']
            ).to(self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        print(f"Model loaded from {path}")
