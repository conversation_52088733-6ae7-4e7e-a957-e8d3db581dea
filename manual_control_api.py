"""
基于AirSim API的手动控制模块（适用于SSH远程连接）
"""
import os
import time
import threading
import numpy as np
from typing import Dict, List, Tuple, Optional
import sys

from environment.drone_env import DroneEnvironment
from config import (
    MANUAL_CONTROL_TURN_STEP, MANUAL_CONTROL_PITCH_STEP, MANUAL_CONTROL_SPEED_STEP,
    MIN_FORWARD_SPEED, MAX_FORWARD_SPEED, MAX_PITCH_ANGLE,
    FORWARD_SPEED, DATA_DIR
)

class APIManualController:
    """
    基于API的手动控制器，通过命令行输入控制无人机
    """
    def __init__(self, env: DroneEnvironment, data_collector: Optional['ManualDataCollector'] = None):
        """
        初始化手动控制器
        
        Args:
            env: 无人机环境
            data_collector: 数据收集器（可选）
        """
        self.env = env
        self.data_collector = data_collector
        
        # 控制状态
        self.current_yaw_delta = 0.0  # 当前转向角增量
        self.current_pitch = 0.0  # 当前俯仰角
        self.current_speed = FORWARD_SPEED  # 当前前进速度
        
        # 运行状态
        self.running = False
        self.collecting_data = False
        
        print("API手动控制器初始化完成")
        print("控制说明:")
        print("  a: 左转10度")
        print("  d: 右转10度") 
        print("  w: 向上俯仰10度")
        print("  s: 向下俯仰10度")
        print("  u: 加速0.2m/s")
        print("  j: 减速0.2m/s")
        print("  space: 切换数据收集模式")
        print("  r: 重置控制状态")
        print("  status: 显示当前状态")
        print("  help: 显示帮助")
        print("  quit: 退出")
        
    def start_control(self):
        """
        开始手动控制
        """
        self.running = True
        print("\nAPI手动控制已启动")
        print("请输入控制命令（输入help查看帮助）:")
        
        while self.running:
            try:
                # 获取用户输入
                command = input("> ").strip().lower()
                
                if not command:
                    continue
                    
                # 处理命令
                self._process_command(command)
                
            except KeyboardInterrupt:
                print("\n用户中断...")
                self.running = False
            except EOFError:
                print("\n输入结束...")
                self.running = False
            except Exception as e:
                print(f"处理命令时出错: {e}")
                
    def _process_command(self, command: str):
        """
        处理用户命令
        """
        if command == 'a':  # 左转
            self.current_yaw_delta -= MANUAL_CONTROL_TURN_STEP
            print(f"左转 {MANUAL_CONTROL_TURN_STEP}度")
        elif command == 'd':  # 右转
            self.current_yaw_delta += MANUAL_CONTROL_TURN_STEP
            print(f"右转 {MANUAL_CONTROL_TURN_STEP}度")
        elif command == 'w':  # 向上俯仰
            self.current_pitch = min(self.current_pitch + MANUAL_CONTROL_PITCH_STEP, MAX_PITCH_ANGLE)
            print(f"向上俯仰，当前俯仰角: {self.current_pitch:.1f}度")
        elif command == 's':  # 向下俯仰
            self.current_pitch = max(self.current_pitch - MANUAL_CONTROL_PITCH_STEP, -MAX_PITCH_ANGLE)
            print(f"向下俯仰，当前俯仰角: {self.current_pitch:.1f}度")
        elif command == 'u':  # 加速
            self.current_speed = min(self.current_speed + MANUAL_CONTROL_SPEED_STEP, MAX_FORWARD_SPEED)
            print(f"加速，当前速度: {self.current_speed:.1f}m/s")
        elif command == 'j':  # 减速
            self.current_speed = max(self.current_speed - MANUAL_CONTROL_SPEED_STEP, MIN_FORWARD_SPEED)
            print(f"减速，当前速度: {self.current_speed:.1f}m/s")
        elif command == 'space':  # 切换数据收集模式
            self._toggle_data_collection()
        elif command == 'r':  # 重置控制状态
            self._reset_control_state()
        elif command == 'status':  # 显示状态
            self._show_status()
        elif command == 'help':  # 显示帮助
            self._show_help()
        elif command == 'quit' or command == 'exit':  # 退出
            self.running = False
            print("正在退出...")
        else:
            print(f"未知命令: {command}，输入help查看帮助")
            
    def _toggle_data_collection(self):
        """
        切换数据收集模式
        """
        if self.data_collector is None:
            print("警告: 未设置数据收集器，无法切换数据收集模式")
            return
            
        self.collecting_data = not self.collecting_data
        mode_str = "开启" if self.collecting_data else "关闭"
        print(f"数据收集模式已{mode_str}")
        
    def _reset_control_state(self):
        """
        重置控制状态
        """
        self.current_yaw_delta = 0.0
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("控制状态已重置")
        
    def _show_status(self):
        """
        显示当前状态
        """
        print(f"\n当前状态:")
        print(f"  转向角增量: {self.current_yaw_delta:.1f}度")
        print(f"  俯仰角: {self.current_pitch:.1f}度")
        print(f"  前进速度: {self.current_speed:.1f}m/s")
        print(f"  数据收集: {'开启' if self.collecting_data else '关闭'}")
        
        # 获取无人机状态
        try:
            drone_state = self.env.client.getMultirotorState(vehicle_name=self.env.drone_name)
            position = drone_state.kinematics_estimated.position
            current_height = -position.z_val
            print(f"  当前高度: {current_height:.2f}m")
            print(f"  当前位置: x={position.x_val:.2f}, y={position.y_val:.2f}")
            print(f"  当前偏航角: {self.env.current_yaw:.1f}度")
        except Exception as e:
            print(f"  无法获取无人机状态: {e}")
        print()
        
    def _show_help(self):
        """
        显示帮助信息
        """
        print("\n控制命令:")
        print("  a       - 左转10度")
        print("  d       - 右转10度")
        print("  w       - 向上俯仰10度")
        print("  s       - 向下俯仰10度")
        print("  u       - 加速0.2m/s")
        print("  j       - 减速0.2m/s")
        print("  space   - 切换数据收集模式")
        print("  r       - 重置控制状态")
        print("  status  - 显示当前状态")
        print("  help    - 显示此帮助")
        print("  quit    - 退出程序")
        print("\n提示: 输入命令后按回车执行\n")
        
    def get_current_action(self) -> Tuple[float, float, float]:
        """
        获取当前动作
        
        Returns:
            (转向角, 俯仰角, 前进速度)
        """
        # 重置转向角增量（每次获取后重置）
        yaw_delta = self.current_yaw_delta
        self.current_yaw_delta = 0.0
        
        return yaw_delta, self.current_pitch, self.current_speed
        
    def is_collecting_data(self) -> bool:
        """
        检查是否正在收集数据
        """
        return self.collecting_data
        
    def reset_after_collision(self):
        """
        碰撞后重置控制状态
        """
        self.current_yaw_delta = 0.0
        self.current_pitch = 0.0
        self.current_speed = FORWARD_SPEED
        print("检测到碰撞，控制状态已重置")


class ManualDataCollector:
    """
    手动数据收集器
    """
    def __init__(self, data_dir: str = DATA_DIR, resume_from: Optional[str] = None):
        """
        初始化手动数据收集器
        
        Args:
            data_dir: 数据存储目录
            resume_from: 要继续的数据文件路径（可选）
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        # 数据存储
        self.observations = []  # 深度图像
        self.heights = []  # 高度值
        self.actions = []  # 动作 [转向角, 俯仰角, 前进速度]
        
        # 如果指定了要继续的文件，加载现有数据
        if resume_from and os.path.exists(resume_from):
            self._load_existing_data(resume_from)
            
    def _load_existing_data(self, filepath: str):
        """
        加载现有数据
        """
        try:
            data = np.load(filepath)
            self.observations = data["observations"].tolist()
            self.heights = data["heights"].tolist()
            self.actions = data["actions"].tolist()
            print(f"已加载现有数据: {len(self.observations)} 个样本")
        except Exception as e:
            print(f"加载现有数据失败: {e}")
            
    def add_sample(self, observation: np.ndarray, height: float, action: Tuple[float, float, float]):
        """
        添加样本
        """
        self.observations.append(observation)
        self.heights.append(height)
        self.actions.append(list(action))
        
    def save_data(self, suffix: str = "manual"):
        """
        保存数据
        """
        if len(self.observations) == 0:
            print("没有数据可保存")
            return
            
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"manual_data_{timestamp}_{suffix}.npz"
        filepath = os.path.join(self.data_dir, filename)
        
        data = {
            "observations": np.array(self.observations),
            "heights": np.array(self.heights),
            "actions": np.array(self.actions)
        }
        
        np.savez_compressed(filepath, **data)
        print(f"数据已保存到: {filepath}")
        print(f"样本数量: {len(self.observations)}")
        
        return filepath
